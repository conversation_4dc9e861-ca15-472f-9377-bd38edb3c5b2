# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Game specific
*.sav
*.save
*.log
*.mp3
*.wav
*.ogg
*.mp4
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tga
*.tif
*.tiff
*.blend
*.blend1
*.blend2
*.blend@
*.blend#
*.blend~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
world_data/
test_output/
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/
benchmark_results/
*.prof
*.profraw
