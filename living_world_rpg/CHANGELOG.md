# Changelog

## [Unreleased]

### Added
- Adopt Elastic Hash Table across core systems.
- Added ElasticHashTable implementation in utils/elastic_hash_table.py.
- Added ElasticDictAdapter for easier migration from dict to ElasticHashTable.
- Added profiling tools for benchmarking ElasticHashTable performance.
- Added comprehensive test suite for entity creation, collision detection, weather system, season system, inventory, and crafting.

### Changed
- Replaced Python's built-in dict with ElasticHashTable in performance-critical areas:
  - Entity.components in core/ecs.py
  - WorldManager.active_regions in world/world_manager.py
  - InventoryComponent.items in core/components.py
  - AIComponent dictionaries (blackboard, personality, emotions, relationships, schedule) in core/components.py
  - MagicComponent.spells in core/components.py
  - EnvironmentEffectComponent.biome_effects in core/components.py
  - ResourceManager.resource_map in world/resource_manager.py
  - EconomyManager.base_prices and dynamic_prices in world/economy_manager.py
  - BiomeClassifier.cache in terrain/biome_classifier.py
  - TerrainGenerator._full_biome_cache in world/terrain_generator.py

### Fixed
- Implemented Funnel Hashing in ElasticHashTable for better collision handling with O(log² 1/δ) worst-case lookups.
- Optimized ElasticDictAdapter to use Python's built-in dict for small collections and ElasticHashTable for large ones.
- Improved performance in collision-heavy scenarios (8.1x slower than dict vs. previous implementations).
- Improved performance for high load factor scenarios (11.3x slower than dict).
- Fixed memory usage by cleaning up deleted entries during resize operations.
- Added safety mechanisms to prevent infinite loops in collision-heavy scenarios.
- Added comprehensive benchmarking tools to compare different hash table implementations.
- Fixed crafting screen crash by implementing missing show_feedback_message method in GameState.
- Fixed inventory component methods for better item management.
- Reduced excessive debug logging from economy system.
- Improved crafting screen UI to properly display player inventory and item information.
- Added default icons for items without images in the crafting screen.
- Fixed case-sensitivity issues in inventory management for items like glacial_crystal and ice_shards.
- Added tooltips with item descriptions when hovering over inventory items.
- Improved inventory component to handle case-insensitive item lookups.
- Fixed ElasticDictAdapter to properly support iteration over keys.
- Optimized inventory lookups with name caching for significant performance improvement.
- Added item categories and filtering to the crafting screen.
- Enhanced seed preview map to show a detailed world with all biomes and terrain features.
- Added visual indicators for mountains, forests, oceans, and other terrain features in the preview map.
- Improved settlement marker with animated pulsing effect and detailed visuals.
- Added comprehensive legend with biome colors and terrain feature explanations.
- Fixed weather system to change only once per day instead of every tick.
- Weather now persists for 1-3 days before changing, with proper seasonal influences.
- Added comprehensive multi-year weather simulation tests to verify seasonal patterns.
- Improved weather system to maintain consistent patterns across years while preserving seasonal variations.
- Fixed crafting screen to properly display and use player's inventory items for crafting requirements.
- Improved case-insensitive item lookup in crafting to correctly count items regardless of capitalization.
- Fixed inventory name cache to properly handle case-insensitive lookups for items like glacial_crystal and ice_shards.
