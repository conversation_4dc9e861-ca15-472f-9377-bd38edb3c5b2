# Contributing to Living World RPG

Thank you for your interest in contributing to Living World RPG! We appreciate your help in making this project better.

## 🚀 Getting Started

1. **Fork** the repository on GitHub
2. **Clone** your fork locally
3. Create a new **branch** for your changes
4. Make your changes and **commit** them
5. **Push** your changes to your fork
6. Open a **pull request**

## 📝 Code Style

We follow these coding standards to maintain consistency:

### Python
- Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide
- Use **Black** for code formatting
- Maximum line length: **88 characters**
- Use **type hints** for all function signatures
- Document all public functions and classes with **docstrings**
- Use **snake_case** for variables and functions
- Use **PascalCase** for class names
- Use **UPPER_CASE** for constants

### Git Commit Messages
- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 50 characters or less
- Reference issues and pull requests liberally
- Consider starting the commit message with an applicable emoji:
  - ✨ `:sparkles:` New feature
  - 🐛 `:bug:` Bug fix
  - ♻️ `:recycle:` Refactoring
  - 📚 `:books:` Documentation
  - 🚧 `:construction:` WIP
  - 🎨 `:art:` UI/UX improvements
  - ⚡ `:zap:` Performance improvements

## 🧪 Testing

- Write tests for new features and bug fixes
- Run all tests before submitting a pull request
- Ensure existing tests pass
- Add appropriate test coverage

## 📦 Dependencies

- Keep dependencies to a minimum
- Document all new dependencies in `requirements.txt`
- Use specific version numbers for all dependencies

## 🐛 Reporting Bugs

When reporting bugs, please include:
1. Steps to reproduce the bug
2. Expected behavior
3. Actual behavior
4. Screenshots if applicable
5. System information (OS, Python version, etc.)

## 💡 Feature Requests

For feature requests, please:
1. Describe the feature you'd like to see
2. Explain why this feature would be valuable
3. Include any relevant examples or references

## 🏷️ Pull Request Process

1. Ensure any install or build dependencies are removed before the end of the layer when doing a build
2. Update the README.md with details of changes to the interface
3. Update the CHANGELOG.md with details of changes
4. You may merge the Pull Request once you have the sign-off of two other developers, or if you do not have permission to do that, you may request the reviewer to merge it for you

## 📜 Code of Conduct

This project and everyone participating in it is governed by our [Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code.

## 🙋 Getting Help

If you need help or have questions, please open an issue or join our community chat.

## 📄 License

By contributing, you agree that your contributions will be licensed under its MIT License.
