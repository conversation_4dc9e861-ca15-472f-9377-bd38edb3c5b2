# Development Guide

Welcome to the Living World RPG development team! This guide will help you set up your development environment and understand our workflow.

## 🛠️ Development Setup

### Prerequisites

- Python 3.9 or higher
- Git
- pip (Python package manager)

### Setting Up the Environment

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/living-world-rpg.git
   cd living-world-rpg
   ```

2. **Create and activate a virtual environment**
   ```bash
   # On macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   
   # On Windows
   python -m venv venv
   .\venv\Scripts\activate
   ```

3. **Install development dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install the package in development mode**
   ```bash
   pip install -e .
   ```

## 🧪 Running Tests

We use `pytest` for testing. To run all tests:

```bash
pytest
```

To run tests with coverage report:

```bash
pytest --cov=src --cov-report=term-missing
```

## 🧹 Code Quality

We use several tools to maintain code quality. Before committing, please run:

```bash
# Format code with Black
black src tests

# Sort imports with isort
isort src tests

# Check for common issues with flake8
flake8 src tests

# Run static type checking with mypy
mypy src
```

## 🗂 Project Structure

```
src/
├── core/           # Core game systems (ECS, game loop, etc.)
├── world/          # World generation and simulation
├── entities/       # Game entities and components
├── systems/        # ECS systems
├── ui/             # User interface components
└── main.py         # Entry point

tests/             # Test files
assets/            # Game assets (images, sounds, etc.)
data/              # Game data files (JSON, etc.)
docs/              # Documentation
```

## 🚀 Running the Game

To start the game:

```bash
python -m src.main
```

## 🧭 Development Workflow

1. **Create a new branch** for your feature or bugfix:
   ```bash
   git checkout -b feature/your-feature-name
   # or
   git checkout -b bugfix/issue-number-description
   ```

2. **Make your changes** and ensure all tests pass.

3. **Commit your changes** with a descriptive message:
   ```bash
   git add .
   git commit -m "Add feature: brief description of changes"
   ```

4. **Push your changes** to your fork:
   ```bash
   git push origin your-branch-name
   ```

5. **Open a pull request** against the `main` branch.

## 📝 Documentation

We use Sphinx for documentation. To build the documentation:

```bash
cd docs
make html
```

The documentation will be available in `docs/_build/html/index.html`.

## 🐛 Reporting Issues

If you find a bug or have a feature request, please open an issue on GitHub with:
- A clear title and description
- Steps to reproduce the issue (if applicable)
- Expected vs. actual behavior
- Screenshots or error messages (if applicable)

## 🤝 Code of Conduct

Please note that this project is governed by the [Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code.

## 📜 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
