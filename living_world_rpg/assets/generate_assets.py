#!/usr/bin/env python3
"""
Generate placeholder icons for all items in Living World RPG.
Creates 64x64 PNGs colored by type and bordered by rarity.
"""
import json
import os
from PIL import Image, ImageDraw

ASSETS_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_PATH = os.path.join(ASSETS_DIR, '..', 'data', 'items.json')
ICON_SIZE = (64, 64)

# Color mappings
TYPE_COLORS = {
    'resource': (160, 120, 90),
    'consumable': (200, 80, 80),
    'spell_scroll': (150, 50, 200),
    'utility': (200, 160, 0),
    'equipment': (80, 80, 200),
    'crafting': (0, 160, 0),
}
RARITY_COLORS = {
    'common': (200, 200, 200),
    'uncommon': (0, 200, 0),
    'rare': (0, 120, 200),
    'legendary': (200, 80, 200),
}

def main():
    with open(DATA_PATH, 'r') as f:
        items = json.load(f)
    for name, info in items.items():
        bg = TYPE_COLORS.get(info.get('type', ''), (120, 120, 120))
        border = RARITY_COLORS.get(info.get('rarity', ''), (100, 100, 100))
        img = Image.new('RGBA', ICON_SIZE, bg + (255,))
        draw = ImageDraw.Draw(img)
        # border
        draw.rectangle([0, 0, ICON_SIZE[0]-1, ICON_SIZE[1]-1], outline=border, width=3)
        # central circle
        r = ICON_SIZE[0] // 4
        c = (ICON_SIZE[0]//2, ICON_SIZE[1]//2)
        draw.ellipse([c[0]-r, c[1]-r, c[0]+r, c[1]+r], outline=(255,255,255), width=2)
        path = os.path.join(ASSETS_DIR, f"{name}.png")
        img.save(path)
        print(f"Generated {path}")

if __name__ == '__main__':
    main()
