# API Reference

## Core Systems

### ECS (Entity Component System)

The game uses an Entity-Component-System architecture for managing game objects and their behaviors.

#### Entity
- `Entity(id: str)` - Base class for all game objects
  - `add_component(component: Component) -> None`
  - `get_component(component_type: Type[Component]) -> Optional[Component]`
  - `has_component(component_type: Type[Component]) -> bool`
  - `remove_component(component_type: Type[Component]) -> None`

#### Component
- `Component` - Base class for all components
  - `entity: Entity` - Reference to the owning entity

#### System
- `System(priority: int = 0)` - Base class for all systems
  - `update(dt: float) -> None` - Called every frame
  - `on_entity_added(entity: Entity) -> None` - Called when an entity is added to the system
  - `on_entity_removed(entity: Entity) -> None` - Called when an entity is removed from the system

## World Generation

### TerrainGenerator
- `TerrainGenerator(seed: int = None)`
  - `generate_chunk(x: int, y: int) -> Chunk` - Generates a chunk at the specified coordinates
  - `get_biome(x: int, y: int) -> Biome` - Gets the biome at the specified coordinates
  - `get_elevation(x: int, y: int) -> float` - Gets the elevation at the specified coordinates

### Biome
- `Biome` - Represents a biome type
  - `name: str` - Name of the biome
  - `color: Tuple[int, int, int]` - RGB color for rendering
  - `temperature_range: Tuple[float, float]` - Temperature range for this biome
  - `moisture_range: Tuple[float, float]` - Moisture range for this biome

## Game Systems

### Combat System
- `CombatSystem` - Handles combat between entities
  - `attack(attacker: Entity, defender: Entity) -> None` - Performs an attack
  - `calculate_damage(attacker: Entity, defender: Entity) -> int` - Calculates damage for an attack

### Inventory System
- `InventoryComponent` - Manages an entity's inventory
  - `add_item(item: Item, quantity: int = 1) -> None`
  - `remove_item(item: str, quantity: int = 1) -> None`
  - `has_item(item: str, quantity: int = 1) -> bool`
  - `get_item_count(item: str) -> int`

### AI System
- `AISystem` - Manages AI behavior for NPCs
  - `update(dt: float) -> None` - Updates all AI entities
  - `set_behavior(entity: Entity, behavior: str) -> None` - Sets the behavior for an entity

## UI System

### UIManager
- `UIManager()` - Manages the game's UI
  - `show_dialog(text: str, options: List[str]) -> None` - Shows a dialog with options
  - `show_message(text: str, duration: float = 3.0) -> None` - Shows a temporary message
  - `update(dt: float) -> None` - Updates the UI

## Data Structures

### SpatialHash
- `SpatialHash(cell_size: int)` - Spatial partitioning for efficient collision detection
  - `add(entity: Entity, x: float, y: float) -> None`
  - `remove(entity: Entity) -> None`
  - `get_nearby(x: float, y: float, radius: float) -> List[Entity]`

### EventBus
- `EventBus()` - Pub/sub system for game events
  - `subscribe(event_type: str, callback: Callable) -> None`
  - `unsubscribe(event_type: str, callback: Callable) -> None`
  - `publish(event_type: str, **kwargs) -> None`

## Utility Functions

### Math Utilities
- `lerp(a: float, b: float, t: float) -> float` - Linear interpolation
- `clamp(value: float, min_val: float, max_val: float) -> float` - Clamps a value between min and max
- `distance(x1: float, y1: float, x2: float, y2: float) -> float` - Calculates distance between two points

### Random Utilities
- `random_int(min_val: int, max_val: int) -> int` - Random integer in range [min_val, max_val]
- `random_float(min_val: float, max_val: float) -> float` - Random float in range [min_val, max_val]
- `random_choice(sequence: Sequence[Any]) -> Any` - Random element from sequence

## Configuration

### Settings
- `Settings` - Game configuration
  - `WINDOW_WIDTH: int` - Width of the game window
  - `WINDOW_HEIGHT: int` - Height of the game window
  - `FPS: int` - Target frames per second
  - `TILE_SIZE: int` - Size of a tile in pixels
  - `CHUNK_SIZE: int` - Size of a chunk in tiles
