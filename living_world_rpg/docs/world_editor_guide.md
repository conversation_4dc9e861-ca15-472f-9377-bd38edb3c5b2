# World Editor Guide

## Overview

The World Editor is a powerful tool for creating, visualizing, and modifying the game world. It provides a comprehensive set of features for terrain generation, biome placement, resource distribution, and detailed inspection of the world's properties.

## Getting Started

### Launching the World Editor

To launch the World Editor, run the following command from the game's root directory:

```bash
python src/world_editor.py
```

### Interface Overview

The World Editor interface is divided into two main sections:

1. **Control Panel** (left side): Contains all the parameters, toggles, and buttons for controlling the world generation and visualization.
2. **Map View** (right side): Displays the generated world with various visualization options.

## Control Panel Features

### World Generation Parameters

These parameters control how the world is generated:

- **Seed**: Determines the random seed used for world generation. The same seed will always produce the same world.
- **Chunk Size**: Controls the size of each chunk in the world.
- **Edge Fade Strength**: Controls how strongly the edges of the world fade to ocean.
- **Sea Level**: Determines the height threshold for ocean tiles.

### Toggle Buttons

These buttons toggle various visualization features:

- **Show Grid**: Displays a grid overlay on the map.
- **Show Biomes**: Displays biome colors on the map.
- **Show Resources**: Displays resource indicators on the map.
- **Show Settlements**: Displays settlement locations on the map.
- **Show Creatures**: Displays creature locations on the map.
- **Chunk Mode**: When enabled, generates and displays a single chunk instead of the entire world.
- **Detailed View**: When enabled, shows detailed information about each tile.

### Region Selection

When Chunk Mode is enabled, these controls allow you to select which region to display:

- **Region X**: The X coordinate of the region to display.
- **Region Y**: The Y coordinate of the region to display.

### Visualization Controls

- **Temp Overlay**: Controls the transparency of the temperature overlay.
- **Detail Level**: Controls the level of detail shown on the map:
  - Level 0: Biomes only
  - Level 1: Biomes, settlements, and resources
  - Level 2: Full detail including creatures and detailed tile information

### Action Buttons

- **Random Seed**: Generates a new random seed.
- **Regenerate**: Regenerates the world with the current parameters.
- **Reset View**: Resets the map view to the default position and zoom level.
- **Save Preview**: Saves the current map view as an image file.

## Map View Features

### Navigation

- **Pan**: Click and drag with the left mouse button to pan the view.
- **Zoom**: Use the mouse wheel to zoom in and out, or use the + and - keys.
- **Arrow Keys**: Use the arrow keys to pan the view.

### Tile Inspection

Hover over a tile to see detailed information about it in the Tile Inspector panel, including:

- Biome type
- Elevation
- Temperature
- Rainfall
- Resources

### Painting Tool

Press the 'P' key to toggle the painting tool. When active, you can click on tiles to change their biome to the currently selected biome.

## Advanced Features

### Biome Palette Editor

If there are missing biome colors, a warning will appear at the bottom of the control panel. Click the "Edit Palette" button to open the Biome Palette Editor, where you can:

- Assign colors to biomes
- Import/export color palettes
- Preview biome colors

### Debug Information

The editor displays debug information at the top of the map view, including:

- FPS (Frames Per Second)
- Number of rendered tiles
- Paint mode status

## Tips and Tricks

1. **Performance**: For better performance when working with large worlds, reduce the Detail Level and disable features you don't need.

2. **Chunk Mode**: Use Chunk Mode to focus on a specific region of the world for detailed editing.

3. **Saving Work**: Use the Save Preview button to save your work as an image file. Note that this doesn't save the actual world data, just an image of the current view.

4. **Keyboard Shortcuts**:
   - P: Toggle paint mode
   - +/-: Zoom in/out
   - Arrow keys: Pan the view

## Troubleshooting

### Common Issues

1. **Missing Biome Colors**: If you see warnings about missing biome colors, use the Biome Palette Editor to assign colors to those biomes.

2. **Performance Issues**: If the editor is running slowly, try:
   - Reducing the chunk size
   - Lowering the detail level
   - Disabling features you don't need

3. **Crashes**: If the editor crashes, check the console output for error messages. Common causes include:
   - Invalid parameter values
   - Missing data files
   - Memory issues with very large worlds

## Technical Details

The World Editor uses the same terrain generation system as the main game, ensuring consistency between the editor and the game. It loads all game data using the same initialization logic, so any changes to the game's data will be reflected in the editor.

The editor supports undo/redo for biome painting operations, allowing you to experiment with different biome layouts without fear of making permanent changes.

## Command Line Arguments

The World Editor supports the following command line arguments:

```bash
python src/world_editor.py --seed=12345 --chunk-size=150 --debug
```

- `--seed`: Sets the initial seed value
- `--chunk-size`: Sets the initial chunk size
- `--debug`: Enables additional debug output

## Conclusion

The World Editor is a powerful tool for creating and customizing the game world. With its comprehensive set of features, you can create unique and detailed worlds for your game. Experiment with different parameters and visualization options to find the perfect world for your adventure!
