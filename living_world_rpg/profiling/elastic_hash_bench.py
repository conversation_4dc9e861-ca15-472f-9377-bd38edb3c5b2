import sys
import time
import random
import statistics
from pathlib import Path

# Add the src directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from src.utils.elastic_hash_table import ElasticHashTable
from src.utils.elastic_dict_adapter import ElasticDictAdapter

def benchmark_insertion(data_size, iterations=5):
    """Benchmark insertion performance."""
    print(f"Benchmarking insertion of {data_size} items...")

    # Generate test data
    keys = [f"key_{i}" for i in range(data_size)]
    values = [f"value_{i}" for i in range(data_size)]

    # Benchmark dict
    dict_times = []
    for _ in range(iterations):
        start_time = time.time()
        d = {}
        for k, v in zip(keys, values):
            d[k] = v
        dict_times.append(time.time() - start_time)

    # Benchmark ElasticHashTable
    eht_times = []
    for _ in range(iterations):
        start_time = time.time()
        eht = ElasticHashTable(size=max(128, data_size // 2))
        for k, v in zip(keys, values):
            eht.insert(k, v)
        eht_times.append(time.time() - start_time)

    # Benchmark ElasticDictAdapter
    eda_times = []
    for _ in range(iterations):
        start_time = time.time()
        eda = ElasticDictAdapter(size=max(128, data_size // 2))
        for k, v in zip(keys, values):
            eda[k] = v
        eda_times.append(time.time() - start_time)

    print(f"Dict median time: {statistics.median(dict_times):.6f}s")
    print(f"ElasticHashTable median time: {statistics.median(eht_times):.6f}s")
    print(f"ElasticDictAdapter median time: {statistics.median(eda_times):.6f}s")
    print(f"EHT vs Dict: {statistics.median(eht_times)/statistics.median(dict_times):.2f}x")
    print(f"EDA vs Dict: {statistics.median(eda_times)/statistics.median(dict_times):.2f}x")
    print()

def benchmark_lookup(data_size, lookup_count, iterations=5):
    """Benchmark lookup performance."""
    print(f"Benchmarking {lookup_count} lookups in {data_size} items...")

    # Generate test data
    keys = [f"key_{i}" for i in range(data_size)]
    values = [f"value_{i}" for i in range(data_size)]

    # Create data structures
    d = {}
    eht = ElasticHashTable(size=max(128, data_size // 2))
    eda = ElasticDictAdapter(size=max(128, data_size // 2))

    for k, v in zip(keys, values):
        d[k] = v
        eht.insert(k, v)
        eda[k] = v

    # Generate lookup keys (some existing, some not)
    lookup_keys = []
    for _ in range(lookup_count):
        if random.random() < 0.9:  # 90% existing keys
            lookup_keys.append(random.choice(keys))
        else:  # 10% non-existing keys
            lookup_keys.append(f"nonexistent_{random.randint(0, 1000)}")

    # Benchmark dict
    dict_times = []
    for _ in range(iterations):
        start_time = time.time()
        for k in lookup_keys:
            _ = d.get(k)
        dict_times.append(time.time() - start_time)

    # Benchmark ElasticHashTable
    eht_times = []
    for _ in range(iterations):
        start_time = time.time()
        for k in lookup_keys:
            _ = eht.get(k)
        eht_times.append(time.time() - start_time)

    # Benchmark ElasticDictAdapter
    eda_times = []
    for _ in range(iterations):
        start_time = time.time()
        for k in lookup_keys:
            _ = eda.get(k)
        eda_times.append(time.time() - start_time)

    print(f"Dict median time: {statistics.median(dict_times):.6f}s")
    print(f"ElasticHashTable median time: {statistics.median(eht_times):.6f}s")
    print(f"ElasticDictAdapter median time: {statistics.median(eda_times):.6f}s")
    print(f"EHT vs Dict: {statistics.median(eht_times)/statistics.median(dict_times):.2f}x")
    print(f"EDA vs Dict: {statistics.median(eda_times)/statistics.median(dict_times):.2f}x")
    print()

def benchmark_high_load_factor(iterations=5):
    """Benchmark performance at high load factors."""
    print("Benchmarking performance at high load factors (90%)...")

    data_size = 115  # Will give ~90% load factor in a 128-size table

    # Generate test data
    keys = [f"key_{i}" for i in range(data_size)]
    values = [f"value_{i}" for i in range(data_size)]

    # Create data structures
    d = {}
    eht = ElasticHashTable(size=128)  # Fixed size to test high load
    eda = ElasticDictAdapter(size=128)  # Fixed size to test high load

    for k, v in zip(keys, values):
        d[k] = v
        eht.insert(k, v)
        eda[k] = v

    # Generate lookup keys
    lookup_count = 1000
    lookup_keys = []
    for _ in range(lookup_count):
        if random.random() < 0.9:  # 90% existing keys
            lookup_keys.append(random.choice(keys))
        else:  # 10% non-existing keys
            lookup_keys.append(f"nonexistent_{random.randint(0, 1000)}")

    # Benchmark dict
    dict_times = []
    for _ in range(iterations):
        start_time = time.time()
        for k in lookup_keys:
            _ = d.get(k)
        dict_times.append(time.time() - start_time)

    # Benchmark ElasticHashTable
    eht_times = []
    for _ in range(iterations):
        start_time = time.time()
        for k in lookup_keys:
            _ = eht.get(k)
        eht_times.append(time.time() - start_time)

    # Benchmark ElasticDictAdapter
    eda_times = []
    for _ in range(iterations):
        start_time = time.time()
        for k in lookup_keys:
            _ = eda.get(k)
        eda_times.append(time.time() - start_time)

    print(f"Dict median time: {statistics.median(dict_times):.6f}s")
    print(f"ElasticHashTable median time: {statistics.median(eht_times):.6f}s")
    print(f"ElasticDictAdapter median time: {statistics.median(eda_times):.6f}s")
    print(f"EHT vs Dict: {statistics.median(eht_times)/statistics.median(dict_times):.2f}x")
    print(f"EDA vs Dict: {statistics.median(eda_times)/statistics.median(dict_times):.2f}x")
    print()

def benchmark_collision_heavy(iterations=5):
    """Benchmark performance with many hash collisions."""
    print("Benchmarking performance with heavy hash collisions...")

    # Create a class with controlled hash collisions
    class CollisionKey:
        def __init__(self, value, hash_value):
            self.value = value
            self.hash_value = hash_value

        def __hash__(self):
            return self.hash_value

        def __eq__(self, other):
            if isinstance(other, CollisionKey):
                return self.value == other.value
            return False

        def __repr__(self):
            return f"CollisionKey({self.value}, {self.hash_value})"

    data_size = 50  # Smaller dataset to avoid infinite loops

    # Generate test data with many collisions but not too extreme
    keys = [CollisionKey(i, i % 5) for i in range(data_size)]  # 5 unique hash values
    values = [f"value_{i}" for i in range(data_size)]

    # Create data structures
    d = {}
    eht = ElasticHashTable(size=256)  # Larger initial size
    eda = ElasticDictAdapter(size=256)  # Larger initial size

    # Insert all items
    try:
        for k, v in zip(keys, values):
            d[k] = v
            eht.insert(k, v)
            eda[k] = v
        print(f"Successfully inserted {data_size} items with collisions")
    except Exception as e:
        print(f"Error during insertion: {e}")
        return

    # Generate lookup keys
    lookup_count = 1000
    lookup_keys = []
    for _ in range(lookup_count):
        lookup_keys.append(random.choice(keys))

    # Benchmark dict
    dict_times = []
    for _ in range(iterations):
        start_time = time.time()
        for k in lookup_keys:
            _ = d.get(k)
        dict_times.append(time.time() - start_time)

    # Benchmark ElasticHashTable
    eht_times = []
    for _ in range(iterations):
        start_time = time.time()
        for k in lookup_keys:
            _ = eht.get(k)
        eht_times.append(time.time() - start_time)

    # Benchmark ElasticDictAdapter
    eda_times = []
    for _ in range(iterations):
        start_time = time.time()
        for k in lookup_keys:
            _ = eda.get(k)
        eda_times.append(time.time() - start_time)

    print(f"Dict median time: {statistics.median(dict_times):.6f}s")
    print(f"ElasticHashTable median time: {statistics.median(eht_times):.6f}s")
    print(f"ElasticDictAdapter median time: {statistics.median(eda_times):.6f}s")
    print(f"EHT vs Dict: {statistics.median(eht_times)/statistics.median(dict_times):.2f}x")
    print(f"EDA vs Dict: {statistics.median(eda_times)/statistics.median(dict_times):.2f}x")
    print()

if __name__ == "__main__":
    print("=== ElasticHashTable Benchmarks ===")
    print()

    # Small data set
    benchmark_insertion(100)
    benchmark_lookup(100, 1000)

    # Medium data set
    benchmark_insertion(1000)
    benchmark_lookup(1000, 1000)

    # Large data set
    benchmark_insertion(10000)
    benchmark_lookup(10000, 1000)

    # High load factor
    benchmark_high_load_factor()

    # Collision-heavy scenario
    benchmark_collision_heavy()
