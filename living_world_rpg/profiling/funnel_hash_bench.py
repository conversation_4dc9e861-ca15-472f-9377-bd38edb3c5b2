#!/usr/bin/env python3
"""
Benchmark script for comparing the performance of Funnel Hashing implementation
against Python's built-in dict and the previous ElasticHashTable implementation.
"""

import sys
import time
import random
import string
import math
from collections import defaultdict

# Add the src directory to the path so we can import our modules
sys.path.append('/Users/<USER>/RPG Game/living_world_rpg')

from src.utils.elastic_hash_table import ElasticHashTable
from src.utils.elastic_dict_adapter import ElasticDictAdapter

# Create a class to simulate the previous implementation for comparison
class DoubleHashTable:
    """Previous implementation using double hashing."""
    def __init__(self, size: int = 128):
        self.size = self._next_power_of_2(size)
        self.mask = self.size - 1
        self.load_factor = 0.65
        self.count = 0
        self.deleted_count = 0
        self.keys = [None] * self.size
        self.values = [None] * self.size
        self.DELETED = object()
        self.MAX_PROBES = min(64, self.size // 2)

    def _next_power_of_2(self, n):
        return 1 if n == 0 else 2**(n - 1).bit_length()

    def _hash_key(self, key):
        h = hash(key) & 0x7FFFFFFF
        return h if h != 0 else 1

    def _hash2(self, key):
        h = (hash(str(key)) & 0x7FFFFFFF) % (self.size - 1)
        return h + 1

    def insert(self, key, value):
        if self.count + self.deleted_count >= int(self.size * self.load_factor):
            self._resize(self.size * 2)

        hash_val = self._hash_key(key)
        step = self._hash2(key)
        idx = hash_val & self.mask
        first_deleted = -1

        for i in range(self.MAX_PROBES):
            if self.keys[idx] == key:
                self.values[idx] = value
                return

            if first_deleted == -1 and self.keys[idx] is self.DELETED:
                first_deleted = idx

            if self.keys[idx] is None:
                if first_deleted != -1:
                    idx = first_deleted
                    self.deleted_count -= 1

                self.keys[idx] = key
                self.values[idx] = value
                self.count += 1
                return

            idx = (idx + step) & self.mask

        self._resize(self.size * 2)
        self.insert(key, value)

    def get(self, key):
        hash_val = self._hash_key(key)
        step = self._hash2(key)
        idx = hash_val & self.mask

        for i in range(self.MAX_PROBES):
            if self.keys[idx] == key:
                return self.values[idx]

            if self.keys[idx] is None:
                return None

            idx = (idx + step) & self.mask

        return None

    def remove(self, key):
        hash_val = self._hash_key(key)
        step = self._hash2(key)
        idx = hash_val & self.mask

        for i in range(self.MAX_PROBES):
            if self.keys[idx] == key:
                self.keys[idx] = self.DELETED
                self.values[idx] = None
                self.count -= 1
                self.deleted_count += 1

                if self.deleted_count > self.size // 4:
                    self._resize(self.size)
                return

            if self.keys[idx] is None:
                return

            idx = (idx + step) & self.mask

    def _resize(self, new_size=None):
        if new_size is None:
            new_size = self.size * 2

        old_size = self.size
        old_keys = self.keys
        old_values = self.values

        self.size = self._next_power_of_2(new_size)
        self.mask = self.size - 1
        self.keys = [None] * self.size
        self.values = [None] * self.size
        self.count = 0
        self.deleted_count = 0
        self.MAX_PROBES = min(64, self.size // 2)

        for i in range(old_size):
            if old_keys[i] is not None and old_keys[i] is not self.DELETED:
                self.insert(old_keys[i], old_values[i])

# Utility functions for benchmarking
def generate_random_string(length=10):
    """Generate a random string of fixed length."""
    return ''.join(random.choice(string.ascii_letters) for _ in range(length))

def generate_test_data(size):
    """Generate test data for benchmarking."""
    keys = [generate_random_string() for _ in range(size)]
    values = [f"value_{i}" for i in range(size)]
    return keys, values

def time_operation(func, iterations=1):
    """Time a function call over multiple iterations."""
    start = time.time()
    for _ in range(iterations):
        func()
    end = time.time()
    return (end - start) / iterations

# Benchmark functions
def benchmark_insertion(iterations=5):
    """Benchmark insertion performance."""
    print("Benchmarking insertion performance...")
    results = defaultdict(list)

    for size in [100, 1000, 10000]:
        print(f"Testing with {size} items...")
        keys, values = generate_test_data(size)

        # Python dict
        def test_dict():
            d = {}
            for k, v in zip(keys, values):
                d[k] = v

        # Double Hash Table
        def test_double_hash():
            dht = DoubleHashTable(size)
            for k, v in zip(keys, values):
                dht.insert(k, v)

        # Funnel Hash Table
        def test_funnel_hash():
            fht = ElasticHashTable(size)
            for k, v in zip(keys, values):
                fht.insert(k, v)

        # ElasticDictAdapter
        def test_adapter():
            eda = ElasticDictAdapter(size=size)
            for k, v in zip(keys, values):
                eda[k] = v

        # Time each implementation
        dict_time = time_operation(test_dict, iterations)
        double_hash_time = time_operation(test_double_hash, iterations)
        funnel_hash_time = time_operation(test_funnel_hash, iterations)
        adapter_time = time_operation(test_adapter, iterations)

        # Store results
        results["dict"].append(dict_time)
        results["double_hash"].append(double_hash_time)
        results["funnel_hash"].append(funnel_hash_time)
        results["adapter"].append(adapter_time)

        # Print results
        print(f"  Dict: {dict_time:.6f}s")
        print(f"  Double Hash: {double_hash_time:.6f}s ({double_hash_time/dict_time:.2f}x slower)")
        print(f"  Funnel Hash: {funnel_hash_time:.6f}s ({funnel_hash_time/dict_time:.2f}x slower)")
        print(f"  Adapter: {adapter_time:.6f}s ({adapter_time/dict_time:.2f}x slower)")

    return results

def benchmark_lookup(iterations=5):
    """Benchmark lookup performance."""
    print("\nBenchmarking lookup performance...")
    results = defaultdict(list)

    for size in [100, 1000, 10000]:
        print(f"Testing with {size} items...")
        keys, values = generate_test_data(size)

        # Create data structures
        d = {}
        dht = DoubleHashTable(size)
        fht = ElasticHashTable(size)
        eda = ElasticDictAdapter(size=size)

        # Insert all items
        for k, v in zip(keys, values):
            d[k] = v
            dht.insert(k, v)
            fht.insert(k, v)
            eda[k] = v

        # Generate lookup keys (mix of hits and misses)
        lookup_keys = keys[:size//2] + [generate_random_string() for _ in range(size//2)]
        random.shuffle(lookup_keys)

        # Python dict
        def test_dict():
            for k in lookup_keys:
                try:
                    _ = d[k]
                except KeyError:
                    pass

        # Double Hash Table
        def test_double_hash():
            for k in lookup_keys:
                _ = dht.get(k)

        # Funnel Hash Table
        def test_funnel_hash():
            for k in lookup_keys:
                _ = fht.get(k)

        # ElasticDictAdapter
        def test_adapter():
            for k in lookup_keys:
                try:
                    _ = eda[k]
                except KeyError:
                    pass

        # Time each implementation
        dict_time = time_operation(test_dict, iterations)
        double_hash_time = time_operation(test_double_hash, iterations)
        funnel_hash_time = time_operation(test_funnel_hash, iterations)
        adapter_time = time_operation(test_adapter, iterations)

        # Store results
        results["dict"].append(dict_time)
        results["double_hash"].append(double_hash_time)
        results["funnel_hash"].append(funnel_hash_time)
        results["adapter"].append(adapter_time)

        # Print results
        print(f"  Dict: {dict_time:.6f}s")
        print(f"  Double Hash: {double_hash_time:.6f}s ({double_hash_time/dict_time:.2f}x slower)")
        print(f"  Funnel Hash: {funnel_hash_time:.6f}s ({funnel_hash_time/dict_time:.2f}x slower)")
        print(f"  Adapter: {adapter_time:.6f}s ({adapter_time/dict_time:.2f}x slower)")

    return results

def benchmark_high_load_factor(iterations=5):
    """Benchmark performance with high load factors."""
    print("\nBenchmarking performance with high load factors...")
    results = defaultdict(list)

    for size in [100, 1000]:
        print(f"Testing with {size} items at high load factor...")
        keys, values = generate_test_data(size)

        # Create data structures with small initial size
        initial_size = size // 4
        d = {}
        dht = DoubleHashTable(initial_size)
        fht = ElasticHashTable(initial_size)
        eda = ElasticDictAdapter(size=initial_size)

        # Insert all items to reach high load factor
        for k, v in zip(keys, values):
            d[k] = v
            dht.insert(k, v)
            fht.insert(k, v)
            eda[k] = v

        # Generate lookup keys (mix of hits and misses)
        lookup_keys = keys[:size//2] + [generate_random_string() for _ in range(size//2)]
        random.shuffle(lookup_keys)

        # Python dict
        def test_dict():
            for k in lookup_keys:
                try:
                    _ = d[k]
                except KeyError:
                    pass

        # Double Hash Table
        def test_double_hash():
            for k in lookup_keys:
                _ = dht.get(k)

        # Funnel Hash Table
        def test_funnel_hash():
            for k in lookup_keys:
                _ = fht.get(k)

        # ElasticDictAdapter
        def test_adapter():
            for k in lookup_keys:
                try:
                    _ = eda[k]
                except KeyError:
                    pass

        # Time each implementation
        dict_time = time_operation(test_dict, iterations)
        double_hash_time = time_operation(test_double_hash, iterations)
        funnel_hash_time = time_operation(test_funnel_hash, iterations)
        adapter_time = time_operation(test_adapter, iterations)

        # Store results
        results["dict"].append(dict_time)
        results["double_hash"].append(double_hash_time)
        results["funnel_hash"].append(funnel_hash_time)
        results["adapter"].append(adapter_time)

        # Print results
        print(f"  Dict: {dict_time:.6f}s")
        print(f"  Double Hash: {double_hash_time:.6f}s ({double_hash_time/dict_time:.2f}x slower)")
        print(f"  Funnel Hash: {funnel_hash_time:.6f}s ({funnel_hash_time/dict_time:.2f}x slower)")
        print(f"  Adapter: {adapter_time:.6f}s ({adapter_time/dict_time:.2f}x slower)")

    return results

def benchmark_collision_heavy(iterations=5):
    """Benchmark performance with many hash collisions."""
    print("\nBenchmarking performance with heavy hash collisions...")
    results = defaultdict(list)

    # Use string keys that hash to the same value
    # This is a simpler approach that avoids custom classes
    data_size = 50

    # Generate keys with known hash collisions
    # In Python, these strings hash to the same value
    base_keys = ["Aa", "BB"]
    keys = []
    for i in range(data_size):
        keys.append(base_keys[i % len(base_keys)] + str(i))

    values = [f"value_{i}" for i in range(data_size)]

    # Create data structures
    d = {}
    dht = DoubleHashTable(128)
    fht = ElasticHashTable(128)
    eda = ElasticDictAdapter(size=128)

    # Insert all items
    try:
        for k, v in zip(keys, values):
            d[k] = v
            dht.insert(k, v)
            fht.insert(k, v)
            eda[k] = v
        print(f"Successfully inserted {data_size} items with collisions")
    except Exception as e:
        print(f"Error during insertion: {e}")
        return results

    # Generate lookup keys (mix of hits and misses)
    lookup_keys = keys[:data_size//2] + [k + "_miss" for k in keys[data_size//2:]]
    random.shuffle(lookup_keys)

    # Python dict
    def test_dict():
        for k in lookup_keys:
            try:
                _ = d[k]
            except KeyError:
                pass

    # Double Hash Table
    def test_double_hash():
        for k in lookup_keys:
            _ = dht.get(k)

    # Funnel Hash Table
    def test_funnel_hash():
        for k in lookup_keys:
            _ = fht.get(k)

    # ElasticDictAdapter
    def test_adapter():
        for k in lookup_keys:
            try:
                _ = eda[k]
            except KeyError:
                pass

    # Time each implementation
    dict_time = time_operation(test_dict, iterations)
    double_hash_time = time_operation(test_double_hash, iterations)
    funnel_hash_time = time_operation(test_funnel_hash, iterations)
    adapter_time = time_operation(test_adapter, iterations)

    # Store results
    results["dict"].append(dict_time)
    results["double_hash"].append(double_hash_time)
    results["funnel_hash"].append(funnel_hash_time)
    results["adapter"].append(adapter_time)

    # Print results
    print(f"  Dict: {dict_time:.6f}s")
    print(f"  Double Hash: {double_hash_time:.6f}s ({double_hash_time/dict_time:.2f}x slower)")
    print(f"  Funnel Hash: {funnel_hash_time:.6f}s ({funnel_hash_time/dict_time:.2f}x slower)")
    print(f"  Adapter: {adapter_time:.6f}s ({adapter_time/dict_time:.2f}x slower)")

    return results

def main():
    """Run all benchmarks."""
    print("=" * 80)
    print("Benchmarking Funnel Hashing vs Double Hashing vs Python dict")
    print("=" * 80)

    # Run all benchmarks
    insertion_results = benchmark_insertion()
    lookup_results = benchmark_lookup()
    high_load_results = benchmark_high_load_factor()
    collision_results = benchmark_collision_heavy()

    print("\nBenchmark Summary:")
    print("=" * 80)

    # Calculate average slowdown factors
    def avg_slowdown(results, impl):
        return sum(results[impl][i] / results["dict"][i] for i in range(len(results[impl]))) / len(results[impl])

    print("Average Slowdown Factors (compared to Python dict):")
    print(f"  Double Hash: {avg_slowdown(insertion_results, 'double_hash'):.2f}x slower for insertion")
    print(f"  Funnel Hash: {avg_slowdown(insertion_results, 'funnel_hash'):.2f}x slower for insertion")
    print(f"  Adapter: {avg_slowdown(insertion_results, 'adapter'):.2f}x slower for insertion")

    print(f"  Double Hash: {avg_slowdown(lookup_results, 'double_hash'):.2f}x slower for lookup")
    print(f"  Funnel Hash: {avg_slowdown(lookup_results, 'funnel_hash'):.2f}x slower for lookup")
    print(f"  Adapter: {avg_slowdown(lookup_results, 'adapter'):.2f}x slower for lookup")

    print(f"  Double Hash: {avg_slowdown(high_load_results, 'double_hash'):.2f}x slower for high load factor")
    print(f"  Funnel Hash: {avg_slowdown(high_load_results, 'funnel_hash'):.2f}x slower for high load factor")
    print(f"  Adapter: {avg_slowdown(high_load_results, 'adapter'):.2f}x slower for high load factor")

    if collision_results:
        print(f"  Double Hash: {avg_slowdown(collision_results, 'double_hash'):.2f}x slower for collision-heavy")
        print(f"  Funnel Hash: {avg_slowdown(collision_results, 'funnel_hash'):.2f}x slower for collision-heavy")
        print(f"  Adapter: {avg_slowdown(collision_results, 'adapter'):.2f}x slower for collision-heavy")

    print("=" * 80)
    print("Benchmark complete!")

if __name__ == "__main__":
    main()
