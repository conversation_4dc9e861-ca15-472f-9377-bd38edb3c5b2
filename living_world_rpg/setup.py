from pathlib import Path
from setuptools import setup, find_packages

# Read the contents of README.md
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding="utf-8")

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as f:
    requirements = [
        line.strip()
        for line in f.readlines()
        if line.strip() and not line.startswith("#")
    ]

setup(
    name="living_world_rpg",
    version="0.1.0",
    description="A sandbox, turn-based, top-down RPG with a living, procedurally generated world",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Your Name",
    author_email="<EMAIL>",
    url="https://github.com/yourusername/living-world-rpg",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.9",
    install_requires=requirements,
    include_package_data=True,
    package_data={
        "living_world_rpg": ["assets/**/*"],
    },
    entry_points={
        "console_scripts": [
            "living-world-rpg=src.main:main",
        ],
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Games/Entertainment :: Role-Playing",
        "Topic :: Software Development :: Libraries :: pygame",
    ],
    keywords="rpg game procedural-generation sandbox pygame",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/living-world-rpg/issues",
        "Source": "https://github.com/yourusername/living-world-rpg",
    },
)
