# Living World RPG AI Systems

This directory contains the AI systems for the Living World RPG game. These systems provide NPCs with realistic behaviors, decision-making capabilities, daily routines, emotional modeling, and social relationships.

## Overview

The AI systems are organized into several modules:

- **Behavior Trees**: For moment-to-moment decision making
- **GOAP (Goal-Oriented Action Planning)**: For high-level planning
- **Scheduling**: For daily routines and activities
- **Social**: For emotional modeling and social relationships
- **History**: For procedural history generation

## Behavior Trees

Behavior trees provide a hierarchical structure for NPC decision making. They consist of:

- **Selector Nodes**: Execute children until one succeeds
- **Sequence Nodes**: Execute children until one fails
- **Decorator Nodes**: Modify the behavior of a child node
- **Condition Nodes**: Check conditions in the world
- **Action Nodes**: Perform actions in the world

Example:
```python
# Create a simple behavior tree for an NPC
root = Selector([
    Sequence([is_hungry, find_food, eat_food]),
    Sequence([is_tired, find_bed, sleep]),
    wander
])
```

## GOAP (Goal-Oriented Action Planning)

GOAP treats NPC decision making as a planning problem. It consists of:

- **Actions**: With preconditions and effects
- **Goals**: Desired world states
- **Planner**: Finds a sequence of actions to achieve a goal

Example:
```python
# Create a GOAP agent
agent = GoapAgent("Villager")

# Define actions
gather_action = Action("GatherResource")
gather_action.add_precondition("has_resource", False)
gather_action.add_effect("has_resource", True)

# Define a goal
goal = Goal("StoreResources")
goal.add_condition("resources_stored", True)
```

## Scheduling

The scheduling system manages NPC daily routines. It consists of:

- **Activities**: What NPCs can do
- **Schedules**: When NPCs do activities
- **Schedule Manager**: Manages all NPC schedules

Example:
```python
# Create a schedule for a villager
schedule = Schedule("Villager")
schedule.add_entry(TimeOfDay.MORNING, work_field)
schedule.add_entry(TimeOfDay.NOON, eat_lunch)
schedule.add_entry(TimeOfDay.NIGHT, sleep)
```

## Social

The social system models NPC emotions and relationships. It consists of:

- **Emotions**: Joy, sadness, anger, etc.
- **Personality Traits**: Openness, conscientiousness, etc.
- **Relationships**: Between NPCs
- **Social Events**: Interactions between NPCs

Example:
```python
# Set up a relationship
npc.set_relationship("OtherNPC", 0.8)  # Positive relationship

# Change emotions
npc.adjust_emotion("joy", 0.5)
```

## Integration

These AI systems are integrated with the game's ECS (Entity-Component-System) architecture:

1. NPCs have an `AIComponent` that stores their AI state
2. The `AISystem` processes all AI components each frame
3. The AI systems interact with other game systems (movement, combat, etc.)

## Usage

To create an NPC with AI:

```python
# Create an NPC with behavior tree AI
npc = create_npc_with_ai("Villager", x, y, "behavior_tree")

# Create an NPC with GOAP AI
npc = create_npc_with_ai("Merchant", x, y, "goap")
```

To test the AI systems:

```bash
python src/test_ai.py
```
