"""
Behavior Tree implementation for Living World RPG.

This module provides a behavior tree implementation for NPC decision making.
Behavior trees are hierarchical structures of nodes that determine NPC behavior
through a combination of selectors, sequences, and leaf nodes.
"""

from enum import Enum
from typing import List, Dict, Any, Optional, Callable
import logging

class NodeStatus(Enum):
    """Status returned by behavior tree nodes."""
    SUCCESS = 1
    FAILURE = 2
    RUNNING = 3

class BlackboardKey:
    """Keys for the blackboard dictionary."""
    TARGET = "target"
    POSITION = "position"
    HOME_POSITION = "home_position"
    CURRENT_TASK = "current_task"
    FEAR_LEVEL = "fear_level"
    HUNGER_LEVEL = "hunger_level"
    ENERGY_LEVEL = "energy_level"
    CURRENT_TOOL = "current_tool"
    WEATHER = "weather"
    TIME_OF_DAY = "time_of_day"
    SEASON = "season"
    NEARBY_ENTITIES = "nearby_entities"
    NEARBY_RESOURCES = "nearby_resources"
    INVENTORY = "inventory"
    RELATIONSHIPS = "relationships"
    SCHEDULE = "schedule"
    PERSONALITY = "personality"
    EMOTIONS = "emotions"

class Blackboard:
    """
    Shared memory for behavior tree nodes.
    
    The blackboard stores information that can be accessed by any node in the behavior tree.
    """
    
    def __init__(self):
        """Initialize an empty blackboard."""
        self.data: Dict[str, Any] = {}
    
    def set(self, key: str, value: Any) -> None:
        """
        Set a value in the blackboard.
        
        Args:
            key: The key to set.
            value: The value to set.
        """
        self.data[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value from the blackboard.
        
        Args:
            key: The key to get.
            default: The default value to return if the key is not found.
            
        Returns:
            The value associated with the key, or the default value if the key is not found.
        """
        return self.data.get(key, default)
    
    def has(self, key: str) -> bool:
        """
        Check if a key exists in the blackboard.
        
        Args:
            key: The key to check.
            
        Returns:
            True if the key exists, False otherwise.
        """
        return key in self.data
    
    def remove(self, key: str) -> None:
        """
        Remove a key from the blackboard.
        
        Args:
            key: The key to remove.
        """
        if key in self.data:
            del self.data[key]

class BehaviorNode:
    """Base class for all behavior tree nodes."""
    
    def __init__(self, name: str = ""):
        """
        Initialize a behavior node.
        
        Args:
            name: The name of the node.
        """
        self.name = name
        self.parent: Optional['BehaviorNode'] = None
    
    def tick(self, blackboard: Blackboard) -> NodeStatus:
        """
        Execute the node's behavior.
        
        Args:
            blackboard: The shared blackboard.
            
        Returns:
            The status of the node after execution.
        """
        raise NotImplementedError("Subclasses must implement tick()")

class CompositeNode(BehaviorNode):
    """Base class for nodes that have children."""
    
    def __init__(self, name: str = ""):
        """
        Initialize a composite node.
        
        Args:
            name: The name of the node.
        """
        super().__init__(name)
        self.children: List[BehaviorNode] = []
    
    def add_child(self, child: BehaviorNode) -> 'CompositeNode':
        """
        Add a child node.
        
        Args:
            child: The child node to add.
            
        Returns:
            Self, for method chaining.
        """
        self.children.append(child)
        child.parent = self
        return self

class Sequence(CompositeNode):
    """
    Executes children in order until one fails.
    
    A sequence node executes its children in order. If a child succeeds, it moves on to the next child.
    If a child fails, the sequence fails. If all children succeed, the sequence succeeds.
    """
    
    def __init__(self, name: str = ""):
        """
        Initialize a sequence node.
        
        Args:
            name: The name of the node.
        """
        super().__init__(name)
        self.current_child_idx = 0
    
    def tick(self, blackboard: Blackboard) -> NodeStatus:
        """
        Execute children in sequence until one fails.
        
        Args:
            blackboard: The shared blackboard.
            
        Returns:
            SUCCESS if all children succeed, FAILURE if any child fails, RUNNING if a child is still running.
        """
        if not self.children:
            return NodeStatus.SUCCESS
        
        # Start from the current child
        while self.current_child_idx < len(self.children):
            child = self.children[self.current_child_idx]
            status = child.tick(blackboard)
            
            # If the child is still running or failed, return that status
            if status != NodeStatus.SUCCESS:
                if status == NodeStatus.FAILURE:
                    self.current_child_idx = 0  # Reset for next time
                return status
            
            # Child succeeded, move to the next one
            self.current_child_idx += 1
        
        # All children succeeded
        self.current_child_idx = 0  # Reset for next time
        return NodeStatus.SUCCESS

class Selector(CompositeNode):
    """
    Executes children in order until one succeeds.
    
    A selector node executes its children in order. If a child fails, it moves on to the next child.
    If a child succeeds, the selector succeeds. If all children fail, the selector fails.
    """
    
    def __init__(self, name: str = ""):
        """
        Initialize a selector node.
        
        Args:
            name: The name of the node.
        """
        super().__init__(name)
        self.current_child_idx = 0
    
    def tick(self, blackboard: Blackboard) -> NodeStatus:
        """
        Execute children in order until one succeeds.
        
        Args:
            blackboard: The shared blackboard.
            
        Returns:
            SUCCESS if any child succeeds, FAILURE if all children fail, RUNNING if a child is still running.
        """
        if not self.children:
            return NodeStatus.FAILURE
        
        # Start from the current child
        while self.current_child_idx < len(self.children):
            child = self.children[self.current_child_idx]
            status = child.tick(blackboard)
            
            # If the child is still running or succeeded, return that status
            if status != NodeStatus.FAILURE:
                if status == NodeStatus.SUCCESS:
                    self.current_child_idx = 0  # Reset for next time
                return status
            
            # Child failed, move to the next one
            self.current_child_idx += 1
        
        # All children failed
        self.current_child_idx = 0  # Reset for next time
        return NodeStatus.FAILURE

class Decorator(BehaviorNode):
    """Base class for nodes that have a single child and modify its behavior."""
    
    def __init__(self, child: Optional[BehaviorNode] = None, name: str = ""):
        """
        Initialize a decorator node.
        
        Args:
            child: The child node to decorate.
            name: The name of the node.
        """
        super().__init__(name)
        self.child = child
        if child:
            child.parent = self
    
    def set_child(self, child: BehaviorNode) -> 'Decorator':
        """
        Set the child node.
        
        Args:
            child: The child node to set.
            
        Returns:
            Self, for method chaining.
        """
        self.child = child
        child.parent = self
        return self

class Inverter(Decorator):
    """
    Inverts the result of its child.
    
    An inverter node inverts the result of its child. If the child succeeds, the inverter fails.
    If the child fails, the inverter succeeds. If the child is running, the inverter is running.
    """
    
    def tick(self, blackboard: Blackboard) -> NodeStatus:
        """
        Invert the result of the child.
        
        Args:
            blackboard: The shared blackboard.
            
        Returns:
            SUCCESS if the child fails, FAILURE if the child succeeds, RUNNING if the child is running.
        """
        if not self.child:
            return NodeStatus.FAILURE
        
        status = self.child.tick(blackboard)
        
        if status == NodeStatus.SUCCESS:
            return NodeStatus.FAILURE
        elif status == NodeStatus.FAILURE:
            return NodeStatus.SUCCESS
        else:
            return status

class Repeater(Decorator):
    """
    Repeats its child a specified number of times.
    
    A repeater node repeats its child a specified number of times. If the child fails,
    the repeater fails. If the child succeeds, the repeater continues until it has
    repeated the child the specified number of times.
    """
    
    def __init__(self, child: Optional[BehaviorNode] = None, num_repeats: int = 1, name: str = ""):
        """
        Initialize a repeater node.
        
        Args:
            child: The child node to repeat.
            num_repeats: The number of times to repeat the child.
            name: The name of the node.
        """
        super().__init__(child, name)
        self.num_repeats = num_repeats
        self.count = 0
    
    def tick(self, blackboard: Blackboard) -> NodeStatus:
        """
        Repeat the child a specified number of times.
        
        Args:
            blackboard: The shared blackboard.
            
        Returns:
            SUCCESS if the child succeeds the specified number of times, FAILURE if the child fails,
            RUNNING if the child is still running or hasn't been repeated enough times.
        """
        if not self.child:
            return NodeStatus.FAILURE
        
        status = self.child.tick(blackboard)
        
        if status == NodeStatus.FAILURE:
            self.count = 0
            return NodeStatus.FAILURE
        
        if status == NodeStatus.RUNNING:
            return NodeStatus.RUNNING
        
        # Child succeeded
        self.count += 1
        
        if self.count >= self.num_repeats:
            self.count = 0
            return NodeStatus.SUCCESS
        
        return NodeStatus.RUNNING

class Condition(BehaviorNode):
    """
    A leaf node that checks a condition.
    
    A condition node checks a condition and returns SUCCESS if the condition is true,
    FAILURE otherwise.
    """
    
    def __init__(self, condition_func: Callable[[Blackboard], bool], name: str = ""):
        """
        Initialize a condition node.
        
        Args:
            condition_func: A function that takes a blackboard and returns a boolean.
            name: The name of the node.
        """
        super().__init__(name)
        self.condition_func = condition_func
    
    def tick(self, blackboard: Blackboard) -> NodeStatus:
        """
        Check the condition.
        
        Args:
            blackboard: The shared blackboard.
            
        Returns:
            SUCCESS if the condition is true, FAILURE otherwise.
        """
        try:
            if self.condition_func(blackboard):
                return NodeStatus.SUCCESS
            else:
                return NodeStatus.FAILURE
        except Exception as e:
            logging.error(f"Error in condition node {self.name}: {e}")
            return NodeStatus.FAILURE

class Action(BehaviorNode):
    """
    A leaf node that performs an action.
    
    An action node performs an action and returns SUCCESS if the action is completed,
    FAILURE if the action fails, and RUNNING if the action is still in progress.
    """
    
    def __init__(self, action_func: Callable[[Blackboard], NodeStatus], name: str = ""):
        """
        Initialize an action node.
        
        Args:
            action_func: A function that takes a blackboard and returns a NodeStatus.
            name: The name of the node.
        """
        super().__init__(name)
        self.action_func = action_func
    
    def tick(self, blackboard: Blackboard) -> NodeStatus:
        """
        Perform the action.
        
        Args:
            blackboard: The shared blackboard.
            
        Returns:
            The status returned by the action function.
        """
        try:
            return self.action_func(blackboard)
        except Exception as e:
            logging.error(f"Error in action node {self.name}: {e}")
            return NodeStatus.FAILURE

class BehaviorTree:
    """
    A behavior tree for NPC decision making.
    
    A behavior tree is a hierarchical structure of nodes that determine NPC behavior
    through a combination of selectors, sequences, and leaf nodes.
    """
    
    def __init__(self, root: BehaviorNode):
        """
        Initialize a behavior tree.
        
        Args:
            root: The root node of the tree.
        """
        self.root = root
        self.blackboard = Blackboard()
    
    def tick(self) -> NodeStatus:
        """
        Execute the behavior tree.
        
        Returns:
            The status of the root node after execution.
        """
        return self.root.tick(self.blackboard)
