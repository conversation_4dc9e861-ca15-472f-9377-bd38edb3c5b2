"""
Goal-Oriented Action Planning (GOAP) for Living World RPG.

This module provides a GOAP implementation for high-level NPC planning.
GOAP treats NPC actions as a planning problem, where actions have preconditions
and effects, and a planner is used to find a sequence of actions to achieve a goal.
"""

from typing import Dict, List, Set, Tuple, Optional, Callable, Any
import heapq
import logging

class WorldState:
    """
    Represents the state of the world as a set of key-value pairs.
    
    The world state is used to check preconditions and apply effects of actions.
    """
    
    def __init__(self, initial_state: Optional[Dict[str, Any]] = None):
        """
        Initialize a world state.
        
        Args:
            initial_state: Initial key-value pairs for the world state.
        """
        self.state = initial_state or {}
    
    def __eq__(self, other: object) -> bool:
        """
        Check if two world states are equal.
        
        Args:
            other: The other world state to compare with.
            
        Returns:
            True if the states are equal, False otherwise.
        """
        if not isinstance(other, WorldState):
            return False
        return self.state == other.state
    
    def __hash__(self) -> int:
        """
        Get a hash of the world state.
        
        Returns:
            A hash of the world state.
        """
        return hash(frozenset(self.state.items()))
    
    def copy(self) -> 'WorldState':
        """
        Create a copy of the world state.
        
        Returns:
            A new WorldState with the same state.
        """
        return WorldState(self.state.copy())
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value from the world state.
        
        Args:
            key: The key to get.
            default: The default value to return if the key is not found.
            
        Returns:
            The value associated with the key, or the default value if the key is not found.
        """
        return self.state.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """
        Set a value in the world state.
        
        Args:
            key: The key to set.
            value: The value to set.
        """
        self.state[key] = value
    
    def has(self, key: str) -> bool:
        """
        Check if a key exists in the world state.
        
        Args:
            key: The key to check.
            
        Returns:
            True if the key exists, False otherwise.
        """
        return key in self.state
    
    def remove(self, key: str) -> None:
        """
        Remove a key from the world state.
        
        Args:
            key: The key to remove.
        """
        if key in self.state:
            del self.state[key]
    
    def satisfies(self, conditions: Dict[str, Any]) -> bool:
        """
        Check if the world state satisfies a set of conditions.
        
        Args:
            conditions: A dictionary of key-value pairs that must be satisfied.
            
        Returns:
            True if all conditions are satisfied, False otherwise.
        """
        for key, value in conditions.items():
            if key not in self.state or self.state[key] != value:
                return False
        return True

class Action:
    """
    Represents an action that can be performed by an NPC.
    
    An action has preconditions that must be satisfied for the action to be performed,
    and effects that are applied to the world state when the action is performed.
    """
    
    def __init__(self, name: str, cost: float = 1.0):
        """
        Initialize an action.
        
        Args:
            name: The name of the action.
            cost: The cost of performing the action.
        """
        self.name = name
        self.cost = cost
        self.preconditions: Dict[str, Any] = {}
        self.effects: Dict[str, Any] = {}
    
    def add_precondition(self, key: str, value: Any) -> 'Action':
        """
        Add a precondition to the action.
        
        Args:
            key: The key of the precondition.
            value: The value of the precondition.
            
        Returns:
            Self, for method chaining.
        """
        self.preconditions[key] = value
        return self
    
    def add_effect(self, key: str, value: Any) -> 'Action':
        """
        Add an effect to the action.
        
        Args:
            key: The key of the effect.
            value: The value of the effect.
            
        Returns:
            Self, for method chaining.
        """
        self.effects[key] = value
        return self
    
    def check_preconditions(self, world_state: WorldState) -> bool:
        """
        Check if the preconditions of the action are satisfied.
        
        Args:
            world_state: The current world state.
            
        Returns:
            True if all preconditions are satisfied, False otherwise.
        """
        return world_state.satisfies(self.preconditions)
    
    def apply_effects(self, world_state: WorldState) -> WorldState:
        """
        Apply the effects of the action to a world state.
        
        Args:
            world_state: The world state to apply the effects to.
            
        Returns:
            A new world state with the effects applied.
        """
        new_state = world_state.copy()
        for key, value in self.effects.items():
            new_state.set(key, value)
        return new_state
    
    def __str__(self) -> str:
        """
        Get a string representation of the action.
        
        Returns:
            A string representation of the action.
        """
        return f"{self.name} (Cost: {self.cost})"

class Goal:
    """
    Represents a goal that an NPC wants to achieve.
    
    A goal is a set of key-value pairs that the NPC wants to be true in the world state.
    """
    
    def __init__(self, name: str):
        """
        Initialize a goal.
        
        Args:
            name: The name of the goal.
        """
        self.name = name
        self.conditions: Dict[str, Any] = {}
    
    def add_condition(self, key: str, value: Any) -> 'Goal':
        """
        Add a condition to the goal.
        
        Args:
            key: The key of the condition.
            value: The value of the condition.
            
        Returns:
            Self, for method chaining.
        """
        self.conditions[key] = value
        return self
    
    def is_satisfied(self, world_state: WorldState) -> bool:
        """
        Check if the goal is satisfied.
        
        Args:
            world_state: The current world state.
            
        Returns:
            True if all conditions are satisfied, False otherwise.
        """
        return world_state.satisfies(self.conditions)
    
    def __str__(self) -> str:
        """
        Get a string representation of the goal.
        
        Returns:
            A string representation of the goal.
        """
        return f"{self.name}: {self.conditions}"

class PlanNode:
    """
    A node in the planning graph.
    
    A plan node represents a state in the planning process, with a parent node,
    an action that led to this state, and the resulting world state.
    """
    
    def __init__(self, parent: Optional['PlanNode'], action: Optional[Action], world_state: WorldState, cost: float):
        """
        Initialize a plan node.
        
        Args:
            parent: The parent node.
            action: The action that led to this node.
            world_state: The world state at this node.
            cost: The cost to reach this node.
        """
        self.parent = parent
        self.action = action
        self.world_state = world_state
        self.cost = cost
    
    def __lt__(self, other: 'PlanNode') -> bool:
        """
        Compare two plan nodes by cost.
        
        Args:
            other: The other plan node to compare with.
            
        Returns:
            True if this node has a lower cost, False otherwise.
        """
        return self.cost < other.cost

class Planner:
    """
    A planner that finds a sequence of actions to achieve a goal.
    
    The planner uses the A* algorithm to find the shortest path from the current
    world state to a state that satisfies the goal.
    """
    
    def __init__(self):
        """Initialize a planner."""
        self.actions: List[Action] = []
    
    def add_action(self, action: Action) -> None:
        """
        Add an action to the planner.
        
        Args:
            action: The action to add.
        """
        self.actions.append(action)
    
    def plan(self, world_state: WorldState, goal: Goal) -> List[Action]:
        """
        Find a plan to achieve a goal.
        
        Args:
            world_state: The current world state.
            goal: The goal to achieve.
            
        Returns:
            A list of actions that achieve the goal, or an empty list if no plan is found.
        """
        if goal.is_satisfied(world_state):
            return []
        
        # A* search
        open_list: List[Tuple[float, PlanNode]] = []
        closed_set: Set[WorldState] = set()
        
        # Start node
        start_node = PlanNode(None, None, world_state, 0)
        heapq.heappush(open_list, (0, start_node))
        
        while open_list:
            # Get the node with the lowest cost
            _, current_node = heapq.heappop(open_list)
            
            # Check if the goal is satisfied
            if goal.is_satisfied(current_node.world_state):
                # Build the plan by following the parent links
                plan: List[Action] = []
                while current_node.parent is not None:
                    if current_node.action:
                        plan.append(current_node.action)
                    current_node = current_node.parent
                plan.reverse()
                return plan
            
            # Add the current state to the closed set
            closed_set.add(current_node.world_state)
            
            # Try each action
            for action in self.actions:
                # Check if the action's preconditions are satisfied
                if not action.check_preconditions(current_node.world_state):
                    continue
                
                # Apply the action's effects
                new_state = action.apply_effects(current_node.world_state)
                
                # Skip if we've already visited this state
                if new_state in closed_set:
                    continue
                
                # Create a new node
                new_cost = current_node.cost + action.cost
                new_node = PlanNode(current_node, action, new_state, new_cost)
                
                # Heuristic: estimate the cost to the goal
                # For simplicity, we use 0 as the heuristic
                heuristic = 0
                priority = new_cost + heuristic
                
                # Add to the open list
                heapq.heappush(open_list, (priority, new_node))
        
        # No plan found
        return []

class GoapAgent:
    """
    An agent that uses GOAP for decision making.
    
    A GOAP agent has a set of actions it can perform, a set of goals it can pursue,
    and a planner that finds plans to achieve those goals.
    """
    
    def __init__(self, name: str):
        """
        Initialize a GOAP agent.
        
        Args:
            name: The name of the agent.
        """
        self.name = name
        self.planner = Planner()
        self.actions: Dict[str, Action] = {}
        self.goals: Dict[str, Goal] = {}
        self.current_goal: Optional[Goal] = None
        self.current_plan: List[Action] = []
        self.current_action_index = 0
        self.world_state = WorldState()
    
    def add_action(self, action: Action) -> None:
        """
        Add an action to the agent.
        
        Args:
            action: The action to add.
        """
        self.actions[action.name] = action
        self.planner.add_action(action)
    
    def add_goal(self, goal: Goal) -> None:
        """
        Add a goal to the agent.
        
        Args:
            goal: The goal to add.
        """
        self.goals[goal.name] = goal
    
    def set_current_goal(self, goal_name: str) -> bool:
        """
        Set the current goal of the agent.
        
        Args:
            goal_name: The name of the goal to set.
            
        Returns:
            True if the goal was set, False if the goal was not found.
        """
        if goal_name in self.goals:
            self.current_goal = self.goals[goal_name]
            self.current_plan = []
            self.current_action_index = 0
            return True
        return False
    
    def update_world_state(self, key: str, value: Any) -> None:
        """
        Update the agent's world state.
        
        Args:
            key: The key to update.
            value: The new value.
        """
        self.world_state.set(key, value)
    
    def think(self) -> Optional[Action]:
        """
        Think about what action to take next.
        
        Returns:
            The next action to take, or None if no action is available.
        """
        # If we have a current goal and it's satisfied, clear it
        if self.current_goal and self.current_goal.is_satisfied(self.world_state):
            self.current_goal = None
            self.current_plan = []
            self.current_action_index = 0
        
        # If we don't have a current goal, we can't do anything
        if not self.current_goal:
            return None
        
        # If we don't have a current plan, try to make one
        if not self.current_plan:
            self.current_plan = self.planner.plan(self.world_state, self.current_goal)
            self.current_action_index = 0
            
            # If we couldn't make a plan, clear the goal
            if not self.current_plan:
                logging.warning(f"[GOAP] Agent {self.name} couldn't find a plan for goal {self.current_goal.name}")
                self.current_goal = None
                return None
        
        # If we've reached the end of the plan, clear it
        if self.current_action_index >= len(self.current_plan):
            self.current_plan = []
            self.current_action_index = 0
            return None
        
        # Get the next action in the plan
        action = self.current_plan[self.current_action_index]
        
        # Check if the action's preconditions are still satisfied
        if not action.check_preconditions(self.world_state):
            # If not, we need to replan
            logging.info(f"[GOAP] Agent {self.name} needs to replan because preconditions for {action.name} are no longer satisfied")
            self.current_plan = self.planner.plan(self.world_state, self.current_goal)
            self.current_action_index = 0
            
            # If we couldn't make a new plan, clear the goal
            if not self.current_plan:
                logging.warning(f"[GOAP] Agent {self.name} couldn't find a new plan for goal {self.current_goal.name}")
                self.current_goal = None
                return None
            
            # Get the next action in the new plan
            action = self.current_plan[self.current_action_index]
        
        # Increment the action index for next time
        self.current_action_index += 1
        
        return action
    
    def execute_action(self, action: Action) -> bool:
        """
        Execute an action and update the world state.
        
        Args:
            action: The action to execute.
            
        Returns:
            True if the action was executed successfully, False otherwise.
        """
        # Check if the action's preconditions are satisfied
        if not action.check_preconditions(self.world_state):
            logging.warning(f"[GOAP] Agent {self.name} tried to execute action {action.name} but preconditions are not satisfied")
            return False
        
        # Apply the action's effects to the world state
        self.world_state = action.apply_effects(self.world_state)
        logging.info(f"[GOAP] Agent {self.name} executed action {action.name}")
        
        return True
