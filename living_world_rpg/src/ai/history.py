"""
Procedural History Generation for Living World RPG.

This module provides systems for generating procedural history for the game world.
It simulates events like wars, plagues, and the rise and fall of factions over time,
creating a rich backstory for the game world.
"""

from typing import Dict, List, Tuple, Optional, Set, Any
from enum import Enum
import random
import math
import logging
from datetime import datetime

class HistoricalEventType(Enum):
    """Types of historical events that can occur in the world."""
    FOUNDING = 0       # Founding of a settlement or faction
    WAR = 1            # War between factions
    PEACE = 2          # Peace treaty between factions
    PLAGUE = 3         # Disease outbreak
    FAMINE = 4         # Food shortage
    NATURAL_DISASTER = 5  # Earthquake, flood, etc.
    TECHNOLOGICAL_ADVANCE = 6  # New technology or magic
    CULTURAL_SHIFT = 7  # Change in beliefs or customs
    LEADERSHIP_CHANGE = 8  # New ruler or government
    MIGRATION = 9      # Population movement
    CONQUEST = 10      # One faction conquers another
    REBELLION = 11     # Uprising against rulers
    GOLDEN_AGE = 12    # Period of prosperity
    DARK_AGE = 13      # Period of decline

class HistoricalEvent:
    """
    Represents a historical event in the game world.
    
    Historical events have a type, a year, involved factions, and a description.
    They form the basis of the world's history.
    """
    
    def __init__(self, event_type: HistoricalEventType, year: int, description: str):
        """
        Initialize a historical event.
        
        Args:
            event_type: The type of event.
            year: The year the event occurred.
            description: A description of the event.
        """
        self.type = event_type
        self.year = year
        self.description = description
        self.involved_factions: List[str] = []
        self.involved_locations: List[str] = []
        self.significance = random.uniform(0.1, 1.0)  # How important this event is
    
    def add_faction(self, faction_id: str) -> None:
        """
        Add a faction involved in the event.
        
        Args:
            faction_id: The ID of the faction.
        """
        if faction_id not in self.involved_factions:
            self.involved_factions.append(faction_id)
    
    def add_location(self, location_id: str) -> None:
        """
        Add a location involved in the event.
        
        Args:
            location_id: The ID of the location.
        """
        if location_id not in self.involved_locations:
            self.involved_locations.append(location_id)
    
    def __str__(self) -> str:
        """
        Get a string representation of the event.
        
        Returns:
            A string representation of the event.
        """
        return f"Year {self.year}: {self.type.name} - {self.description}"

class Faction:
    """
    Represents a faction in the game world.
    
    Factions are groups of NPCs with shared goals, beliefs, or allegiances.
    They can be nations, guilds, religions, or any other organized group.
    """
    
    def __init__(self, faction_id: str, name: str):
        """
        Initialize a faction.
        
        Args:
            faction_id: The unique ID of the faction.
            name: The name of the faction.
        """
        self.id = faction_id
        self.name = name
        self.founding_year = 0
        self.is_active = True
        self.dissolution_year = None
        self.power = random.uniform(0.1, 1.0)  # Military/political power
        self.wealth = random.uniform(0.1, 1.0)  # Economic resources
        self.territory: List[str] = []  # Location IDs controlled by this faction
        self.allies: List[str] = []  # Faction IDs
        self.enemies: List[str] = []  # Faction IDs
        self.traits: List[str] = []  # Cultural/political traits
    
    def add_territory(self, location_id: str) -> None:
        """
        Add a territory to the faction.
        
        Args:
            location_id: The ID of the location.
        """
        if location_id not in self.territory:
            self.territory.append(location_id)
    
    def remove_territory(self, location_id: str) -> None:
        """
        Remove a territory from the faction.
        
        Args:
            location_id: The ID of the location.
        """
        if location_id in self.territory:
            self.territory.remove(location_id)
    
    def add_ally(self, faction_id: str) -> None:
        """
        Add an ally to the faction.
        
        Args:
            faction_id: The ID of the allied faction.
        """
        if faction_id not in self.allies and faction_id != self.id:
            self.allies.append(faction_id)
            if faction_id in self.enemies:
                self.enemies.remove(faction_id)
    
    def add_enemy(self, faction_id: str) -> None:
        """
        Add an enemy to the faction.
        
        Args:
            faction_id: The ID of the enemy faction.
        """
        if faction_id not in self.enemies and faction_id != self.id:
            self.enemies.append(faction_id)
            if faction_id in self.allies:
                self.allies.remove(faction_id)
    
    def dissolve(self, year: int) -> None:
        """
        Dissolve the faction.
        
        Args:
            year: The year the faction was dissolved.
        """
        self.is_active = False
        self.dissolution_year = year
        self.territory = []
        self.allies = []
        self.enemies = []
    
    def __str__(self) -> str:
        """
        Get a string representation of the faction.
        
        Returns:
            A string representation of the faction.
        """
        status = "Active" if self.is_active else f"Dissolved in {self.dissolution_year}"
        return f"{self.name} (Founded: {self.founding_year}, {status})"

class WorldHistory:
    """
    Represents the history of the game world.
    
    World history is a collection of historical events and factions that have
    existed throughout the world's timeline.
    """
    
    def __init__(self, start_year: int = 0, current_year: int = 1000):
        """
        Initialize world history.
        
        Args:
            start_year: The starting year for the history simulation.
            current_year: The current year in the game world.
        """
        self.start_year = start_year
        self.current_year = current_year
        self.events: List[HistoricalEvent] = []
        self.factions: Dict[str, Faction] = {}
        self.locations: Dict[str, str] = {}  # location_id -> name
    
    def add_event(self, event: HistoricalEvent) -> None:
        """
        Add an event to the history.
        
        Args:
            event: The historical event to add.
        """
        self.events.append(event)
        # Sort events by year
        self.events.sort(key=lambda e: e.year)
    
    def add_faction(self, faction: Faction) -> None:
        """
        Add a faction to the history.
        
        Args:
            faction: The faction to add.
        """
        self.factions[faction.id] = faction
    
    def add_location(self, location_id: str, name: str) -> None:
        """
        Add a location to the history.
        
        Args:
            location_id: The unique ID of the location.
            name: The name of the location.
        """
        self.locations[location_id] = name
    
    def get_events_by_year(self, year: int) -> List[HistoricalEvent]:
        """
        Get all events that occurred in a specific year.
        
        Args:
            year: The year to get events for.
            
        Returns:
            A list of events that occurred in the specified year.
        """
        return [event for event in self.events if event.year == year]
    
    def get_events_by_type(self, event_type: HistoricalEventType) -> List[HistoricalEvent]:
        """
        Get all events of a specific type.
        
        Args:
            event_type: The type of events to get.
            
        Returns:
            A list of events of the specified type.
        """
        return [event for event in self.events if event.type == event_type]
    
    def get_events_by_faction(self, faction_id: str) -> List[HistoricalEvent]:
        """
        Get all events involving a specific faction.
        
        Args:
            faction_id: The ID of the faction.
            
        Returns:
            A list of events involving the specified faction.
        """
        return [event for event in self.events if faction_id in event.involved_factions]
    
    def get_events_by_location(self, location_id: str) -> List[HistoricalEvent]:
        """
        Get all events involving a specific location.
        
        Args:
            location_id: The ID of the location.
            
        Returns:
            A list of events involving the specified location.
        """
        return [event for event in self.events if location_id in event.involved_locations]
    
    def get_active_factions(self) -> List[Faction]:
        """
        Get all active factions.
        
        Returns:
            A list of active factions.
        """
        return [faction for faction in self.factions.values() if faction.is_active]
    
    def get_faction_history(self, faction_id: str) -> List[HistoricalEvent]:
        """
        Get the history of a specific faction.
        
        Args:
            faction_id: The ID of the faction.
            
        Returns:
            A list of events involving the specified faction, sorted by year.
        """
        events = self.get_events_by_faction(faction_id)
        events.sort(key=lambda e: e.year)
        return events
    
    def get_location_history(self, location_id: str) -> List[HistoricalEvent]:
        """
        Get the history of a specific location.
        
        Args:
            location_id: The ID of the location.
            
        Returns:
            A list of events involving the specified location, sorted by year.
        """
        events = self.get_events_by_location(location_id)
        events.sort(key=lambda e: e.year)
        return events
    
    def __str__(self) -> str:
        """
        Get a string representation of the world history.
        
        Returns:
            A string representation of the world history.
        """
        result = f"World History ({self.start_year} - {self.current_year}):\n"
        result += f"Factions: {len(self.factions)}\n"
        result += f"Locations: {len(self.locations)}\n"
        result += f"Events: {len(self.events)}\n"
        return result

class HistoryGenerator:
    """
    Generates procedural history for the game world.
    
    The history generator simulates events like wars, plagues, and the rise and fall
    of factions over time, creating a rich backstory for the game world.
    """
    
    def __init__(self, seed: Optional[int] = None):
        """
        Initialize the history generator.
        
        Args:
            seed: The random seed to use for generation.
        """
        if seed is not None:
            random.seed(seed)
        
        self.faction_names = [
            "Empire of the Sun", "Kingdom of the North", "Southern Confederacy",
            "Eastern Alliance", "Western Union", "Mountain Clans", "Forest Tribes",
            "Desert Nomads", "Coastal Merchants", "Island Federation", "Underground Guild",
            "Order of the Light", "Cult of Shadows", "Arcane College", "Warrior's Brotherhood",
            "Assassin's League", "Thieves' Guild", "Alchemist's Society", "Druid Circle",
            "Paladin Order", "Ranger's Coalition", "Bard's College", "Merchant's Guild"
        ]
        
        self.location_names = [
            "Highcastle", "Deepvale", "Westport", "Eastwatch", "Northkeep", "Southhaven",
            "Greenfield", "Blackwater", "Redmountain", "Blueshore", "Goldcrest", "Silverstream",
            "Ironforge", "Copperhill", "Bronzetown", "Steelworks", "Tinmines", "Leadville",
            "Crystalcave", "Gemstone", "Pearlbay", "Rubycity", "Emeraldforest", "Sapphirecliff"
        ]
        
        self.event_descriptions = {
            HistoricalEventType.FOUNDING: [
                "was founded by settlers seeking new opportunities",
                "was established as a trading post",
                "was created as a military outpost",
                "was founded by religious pilgrims",
                "was established by refugees fleeing conflict"
            ],
            HistoricalEventType.WAR: [
                "declared war over territorial disputes",
                "fought a bloody conflict over resources",
                "engaged in a war of succession",
                "battled in a religious crusade",
                "clashed in a trade war"
            ],
            HistoricalEventType.PEACE: [
                "signed a peace treaty ending years of conflict",
                "formed an alliance against common enemies",
                "established diplomatic relations",
                "agreed to a non-aggression pact",
                "formed a trade agreement"
            ],
            HistoricalEventType.PLAGUE: [
                "was devastated by a mysterious illness",
                "suffered from a magical plague",
                "endured a deadly epidemic",
                "was quarantined due to disease",
                "lost many citizens to sickness"
            ],
            HistoricalEventType.FAMINE: [
                "suffered a severe food shortage",
                "endured years of failed harvests",
                "experienced widespread hunger",
                "was forced to ration food supplies",
                "saw many die from starvation"
            ],
            HistoricalEventType.NATURAL_DISASTER: [
                "was struck by a devastating earthquake",
                "was flooded after heavy rains",
                "was destroyed by a volcanic eruption",
                "was hit by a powerful storm",
                "was consumed by wildfire"
            ],
            HistoricalEventType.TECHNOLOGICAL_ADVANCE: [
                "discovered a new form of magic",
                "invented a revolutionary technology",
                "made breakthroughs in alchemy",
                "developed advanced weaponry",
                "created new agricultural techniques"
            ],
            HistoricalEventType.CULTURAL_SHIFT: [
                "underwent a religious reformation",
                "experienced an artistic renaissance",
                "saw a shift in political ideology",
                "adopted new customs from foreign lands",
                "rejected traditional beliefs"
            ],
            HistoricalEventType.LEADERSHIP_CHANGE: [
                "crowned a new ruler after the old one died",
                "overthrew the government in a coup",
                "elected a new council of leaders",
                "was taken over by a foreign regent",
                "saw power shift to a new dynasty"
            ],
            HistoricalEventType.MIGRATION: [
                "welcomed a large influx of refugees",
                "saw many citizens leave for better opportunities",
                "experienced a mass exodus due to hardship",
                "received settlers from distant lands",
                "was abandoned by most of its population"
            ],
            HistoricalEventType.CONQUEST: [
                "conquered neighboring territories",
                "was subjugated by a powerful empire",
                "annexed weaker settlements",
                "expanded its borders through military force",
                "fell to invading armies"
            ],
            HistoricalEventType.REBELLION: [
                "rose up against oppressive rulers",
                "experienced a slave revolt",
                "saw peasants rebel against nobles",
                "endured civil unrest and riots",
                "fought against occupying forces"
            ],
            HistoricalEventType.GOLDEN_AGE: [
                "entered a period of unprecedented prosperity",
                "experienced a cultural and artistic flowering",
                "saw rapid technological advancement",
                "enjoyed peace and abundance",
                "became a center of learning and innovation"
            ],
            HistoricalEventType.DARK_AGE: [
                "fell into decline and poverty",
                "lost much of its knowledge and technology",
                "suffered under corrupt leadership",
                "was isolated from the outside world",
                "experienced a period of stagnation"
            ]
        }
    
    def generate_faction_name(self) -> str:
        """
        Generate a random faction name.
        
        Returns:
            A randomly generated faction name.
        """
        return random.choice(self.faction_names)
    
    def generate_location_name(self) -> str:
        """
        Generate a random location name.
        
        Returns:
            A randomly generated location name.
        """
        return random.choice(self.location_names)
    
    def generate_event_description(self, event_type: HistoricalEventType, factions: List[Faction] = None, locations: List[str] = None) -> str:
        """
        Generate a description for a historical event.
        
        Args:
            event_type: The type of event.
            factions: The factions involved in the event.
            locations: The locations involved in the event.
            
        Returns:
            A randomly generated event description.
        """
        base_description = random.choice(self.event_descriptions.get(event_type, ["occurred"]))
        
        if factions and len(factions) > 0:
            if len(factions) == 1:
                faction_str = factions[0].name
            elif len(factions) == 2:
                faction_str = f"{factions[0].name} and {factions[1].name}"
            else:
                faction_str = ", ".join([f.name for f in factions[:-1]]) + f", and {factions[-1].name}"
            
            if event_type == HistoricalEventType.FOUNDING:
                return f"{faction_str} {base_description}"
            elif event_type in [HistoricalEventType.WAR, HistoricalEventType.PEACE]:
                return f"{faction_str} {base_description}"
            else:
                return f"{faction_str} {base_description}"
        
        if locations and len(locations) > 0:
            if len(locations) == 1:
                location_str = locations[0]
            elif len(locations) == 2:
                location_str = f"{locations[0]} and {locations[1]}"
            else:
                location_str = ", ".join(locations[:-1]) + f", and {locations[-1]}"
            
            return f"{location_str} {base_description}"
        
        return base_description
    
    def generate_history(self, start_year: int = 0, end_year: int = 1000, num_factions: int = 10, num_locations: int = 20) -> WorldHistory:
        """
        Generate a procedural history for the game world.
        
        Args:
            start_year: The starting year for the history simulation.
            end_year: The ending year for the history simulation.
            num_factions: The number of factions to generate.
            num_locations: The number of locations to generate.
            
        Returns:
            A generated world history.
        """
        history = WorldHistory(start_year, end_year)
        
        # Generate locations
        for i in range(num_locations):
            location_id = f"loc_{i}"
            name = f"{self.generate_location_name()}_{i}"
            history.add_location(location_id, name)
        
        # Generate initial factions
        initial_factions = max(3, num_factions // 3)
        for i in range(initial_factions):
            faction_id = f"fac_{i}"
            name = f"{self.generate_faction_name()}_{i}"
            faction = Faction(faction_id, name)
            faction.founding_year = start_year
            
            # Assign some initial territory
            territories = random.sample(list(history.locations.keys()), min(3, len(history.locations)))
            for territory in territories:
                faction.add_territory(territory)
            
            history.add_faction(faction)
            
            # Create founding event
            event = HistoricalEvent(HistoricalEventType.FOUNDING, start_year, 
                                   self.generate_event_description(HistoricalEventType.FOUNDING, [faction]))
            event.add_faction(faction.id)
            for territory in territories:
                event.add_location(territory)
            history.add_event(event)
        
        # Simulate history year by year
        for year in range(start_year + 1, end_year + 1):
            # Maybe create a new faction
            if len(history.get_active_factions()) < num_factions and random.random() < 0.1:
                faction_id = f"fac_{len(history.factions)}"
                name = f"{self.generate_faction_name()}_{len(history.factions)}"
                faction = Faction(faction_id, name)
                faction.founding_year = year
                
                # Assign some initial territory, possibly taking from existing factions
                available_territories = []
                for loc_id in history.locations.keys():
                    is_claimed = False
                    for f in history.get_active_factions():
                        if loc_id in f.territory:
                            is_claimed = True
                            break
                    if not is_claimed:
                        available_territories.append(loc_id)
                
                if available_territories:
                    territories = random.sample(available_territories, min(2, len(available_territories)))
                    for territory in territories:
                        faction.add_territory(territory)
                
                history.add_faction(faction)
                
                # Create founding event
                event = HistoricalEvent(HistoricalEventType.FOUNDING, year, 
                                       self.generate_event_description(HistoricalEventType.FOUNDING, [faction]))
                event.add_faction(faction.id)
                for territory in territories:
                    event.add_location(territory)
                history.add_event(event)
            
            # Generate random events
            self._generate_random_events(history, year)
            
            # Update faction relationships
            self._update_faction_relationships(history)
            
            # Maybe dissolve some factions
            for faction in list(history.get_active_factions()):
                if not faction.territory and random.random() < 0.5:
                    faction.dissolve(year)
                    
                    # Create dissolution event
                    event = HistoricalEvent(HistoricalEventType.DARK_AGE, year, 
                                           f"{faction.name} collapsed and ceased to exist")
                    event.add_faction(faction.id)
                    history.add_event(event)
        
        return history
    
    def _generate_random_events(self, history: WorldHistory, year: int) -> None:
        """
        Generate random events for a specific year.
        
        Args:
            history: The world history to add events to.
            year: The year to generate events for.
        """
        active_factions = history.get_active_factions()
        
        # Skip if no active factions
        if not active_factions:
            return
        
        # War events
        if len(active_factions) >= 2 and random.random() < 0.1:
            factions = random.sample(active_factions, 2)
            factions[0].add_enemy(factions[1].id)
            factions[1].add_enemy(factions[0].id)
            
            event = HistoricalEvent(HistoricalEventType.WAR, year, 
                                   self.generate_event_description(HistoricalEventType.WAR, factions))
            event.add_faction(factions[0].id)
            event.add_faction(factions[1].id)
            
            # Add involved territories
            for territory in factions[0].territory:
                event.add_location(territory)
            for territory in factions[1].territory:
                event.add_location(territory)
            
            history.add_event(event)
            
            # Maybe one faction conquers territory from the other
            if random.random() < 0.5:
                winner = random.choice(factions)
                loser = factions[0] if winner == factions[1] else factions[1]
                
                if loser.territory:
                    conquered_territory = random.choice(loser.territory)
                    loser.remove_territory(conquered_territory)
                    winner.add_territory(conquered_territory)
                    
                    conquest_event = HistoricalEvent(HistoricalEventType.CONQUEST, year, 
                                                   f"{winner.name} conquered {history.locations.get(conquered_territory, 'a territory')} from {loser.name}")
                    conquest_event.add_faction(winner.id)
                    conquest_event.add_faction(loser.id)
                    conquest_event.add_location(conquered_territory)
                    history.add_event(conquest_event)
        
        # Peace events
        for faction in active_factions:
            if faction.enemies and random.random() < 0.05:
                enemy_id = random.choice(faction.enemies)
                if enemy_id in history.factions:
                    enemy = history.factions[enemy_id]
                    
                    faction.enemies.remove(enemy_id)
                    if faction.id in enemy.enemies:
                        enemy.enemies.remove(faction.id)
                    
                    event = HistoricalEvent(HistoricalEventType.PEACE, year, 
                                           self.generate_event_description(HistoricalEventType.PEACE, [faction, enemy]))
                    event.add_faction(faction.id)
                    event.add_faction(enemy_id)
                    history.add_event(event)
        
        # Plague events
        if random.random() < 0.03:
            affected_faction = random.choice(active_factions)
            
            event = HistoricalEvent(HistoricalEventType.PLAGUE, year, 
                                   self.generate_event_description(HistoricalEventType.PLAGUE, [affected_faction]))
            event.add_faction(affected_faction.id)
            
            for territory in affected_faction.territory:
                event.add_location(territory)
            
            history.add_event(event)
        
        # Natural disaster events
        if random.random() < 0.02:
            affected_faction = random.choice(active_factions)
            
            if affected_faction.territory:
                affected_location = random.choice(affected_faction.territory)
                
                event = HistoricalEvent(HistoricalEventType.NATURAL_DISASTER, year, 
                                       self.generate_event_description(HistoricalEventType.NATURAL_DISASTER, 
                                                                     locations=[history.locations.get(affected_location, "a settlement")]))
                event.add_faction(affected_faction.id)
                event.add_location(affected_location)
                
                history.add_event(event)
        
        # Cultural and technological events
        if random.random() < 0.05:
            affected_faction = random.choice(active_factions)
            
            event_type = random.choice([HistoricalEventType.CULTURAL_SHIFT, HistoricalEventType.TECHNOLOGICAL_ADVANCE])
            
            event = HistoricalEvent(event_type, year, 
                                   self.generate_event_description(event_type, [affected_faction]))
            event.add_faction(affected_faction.id)
            
            history.add_event(event)
        
        # Leadership change events
        if random.random() < 0.08:
            affected_faction = random.choice(active_factions)
            
            event = HistoricalEvent(HistoricalEventType.LEADERSHIP_CHANGE, year, 
                                   self.generate_event_description(HistoricalEventType.LEADERSHIP_CHANGE, [affected_faction]))
            event.add_faction(affected_faction.id)
            
            history.add_event(event)
        
        # Golden age and dark age events
        if random.random() < 0.02:
            affected_faction = random.choice(active_factions)
            
            event_type = HistoricalEventType.GOLDEN_AGE if random.random() < 0.7 else HistoricalEventType.DARK_AGE
            
            event = HistoricalEvent(event_type, year, 
                                   self.generate_event_description(event_type, [affected_faction]))
            event.add_faction(affected_faction.id)
            
            history.add_event(event)
    
    def _update_faction_relationships(self, history: WorldHistory) -> None:
        """
        Update relationships between factions.
        
        Args:
            history: The world history containing the factions.
        """
        active_factions = history.get_active_factions()
        
        # Skip if not enough active factions
        if len(active_factions) < 2:
            return
        
        # Randomly form alliances
        for faction in active_factions:
            if random.random() < 0.05:
                potential_allies = [f for f in active_factions if f.id != faction.id and f.id not in faction.allies and f.id not in faction.enemies]
                
                if potential_allies:
                    ally = random.choice(potential_allies)
                    faction.add_ally(ally.id)
                    ally.add_ally(faction.id)
        
        # Randomly create rivalries
        for faction in active_factions:
            if random.random() < 0.03:
                potential_enemies = [f for f in active_factions if f.id != faction.id and f.id not in faction.enemies and f.id not in faction.allies]
                
                if potential_enemies:
                    enemy = random.choice(potential_enemies)
                    faction.add_enemy(enemy.id)
                    enemy.add_enemy(faction.id)
