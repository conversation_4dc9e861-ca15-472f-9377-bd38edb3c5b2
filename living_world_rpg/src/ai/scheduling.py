"""
Scheduling system for Living World RPG.

This module provides a scheduling system for NPCs to follow daily routines.
NPCs can have schedules that define what activities they should be doing at
different times of the day, and the system can handle overrides for special
events like weather or emergencies.
"""

from typing import Dict, List, Tuple, Optional, Callable, Any
from enum import Enum
import random
import logging

class TimeOfDay(Enum):
    """Time periods during the day."""
    EARLY_MORNING = 0  # 5:00 - 8:00
    MORNING = 1        # 8:00 - 12:00
    NOON = 2           # 12:00 - 14:00
    AFTERNOON = 3      # 14:00 - 18:00
    EVENING = 4        # 18:00 - 22:00
    NIGHT = 5          # 22:00 - 5:00

class WeatherType(Enum):
    """Types of weather."""
    CLEAR = 0
    CLOUDY = 1
    RAINY = 2
    STORMY = 3
    SNOWY = 4
    FOGGY = 5
    WINDY = 6

class EventType(Enum):
    """Types of special events."""
    NORMAL = 0
    FESTIVAL = 1
    MARKET_DAY = 2
    RAID = 3
    FIRE = 4
    PLAGUE = 5
    ROYAL_VISIT = 6

class ActivityPriority(Enum):
    """Priority levels for activities."""
    LOW = 0      # Optional activities
    MEDIUM = 1   # Regular daily activities
    HIGH = 2     # Important activities
    CRITICAL = 3 # Emergency activities

class Activity:
    """
    Represents an activity that an NPC can perform.
    
    An activity has a name, a location, a priority, and optional conditions
    for when it can be performed.
    """
    
    def __init__(self, name: str, location: Tuple[int, int], priority: ActivityPriority = ActivityPriority.MEDIUM):
        """
        Initialize an activity.
        
        Args:
            name: The name of the activity.
            location: The (x, y) coordinates where the activity takes place.
            priority: The priority of the activity.
        """
        self.name = name
        self.location = location
        self.priority = priority
        self.weather_conditions: List[WeatherType] = []
        self.event_conditions: List[EventType] = []
        self.time_variance = 0.0  # Random variance in start time (0.0 - 1.0)
    
    def add_weather_condition(self, weather: WeatherType) -> 'Activity':
        """
        Add a weather condition for this activity.
        
        Args:
            weather: The weather type that allows this activity.
            
        Returns:
            Self, for method chaining.
        """
        self.weather_conditions.append(weather)
        return self
    
    def add_event_condition(self, event: EventType) -> 'Activity':
        """
        Add an event condition for this activity.
        
        Args:
            event: The event type that allows this activity.
            
        Returns:
            Self, for method chaining.
        """
        self.event_conditions.append(event)
        return self
    
    def set_time_variance(self, variance: float) -> 'Activity':
        """
        Set the time variance for this activity.
        
        Args:
            variance: The time variance (0.0 - 1.0).
            
        Returns:
            Self, for method chaining.
        """
        self.time_variance = max(0.0, min(1.0, variance))
        return self
    
    def can_perform(self, weather: WeatherType, event: EventType) -> bool:
        """
        Check if the activity can be performed in the given conditions.
        
        Args:
            weather: The current weather.
            event: The current event.
            
        Returns:
            True if the activity can be performed, False otherwise.
        """
        # If no conditions are specified, the activity can always be performed
        if not self.weather_conditions and not self.event_conditions:
            return True
        
        # Check weather conditions
        weather_ok = not self.weather_conditions or weather in self.weather_conditions
        
        # Check event conditions
        event_ok = not self.event_conditions or event in self.event_conditions
        
        return weather_ok and event_ok
    
    def __str__(self) -> str:
        """
        Get a string representation of the activity.
        
        Returns:
            A string representation of the activity.
        """
        return f"{self.name} at {self.location} (Priority: {self.priority.name})"

class ScheduleEntry:
    """
    Represents an entry in an NPC's schedule.
    
    A schedule entry defines what activity an NPC should be doing during a
    specific time period.
    """
    
    def __init__(self, time_of_day: TimeOfDay, activity: Activity):
        """
        Initialize a schedule entry.
        
        Args:
            time_of_day: The time period for this entry.
            activity: The activity to perform during this time period.
        """
        self.time_of_day = time_of_day
        self.activity = activity
    
    def __str__(self) -> str:
        """
        Get a string representation of the schedule entry.
        
        Returns:
            A string representation of the schedule entry.
        """
        return f"{self.time_of_day.name}: {self.activity}"

class Schedule:
    """
    Represents an NPC's daily schedule.
    
    A schedule is a collection of schedule entries that define what activities
    an NPC should be doing at different times of the day.
    """
    
    def __init__(self, npc_name: str):
        """
        Initialize a schedule.
        
        Args:
            npc_name: The name of the NPC this schedule belongs to.
        """
        self.npc_name = npc_name
        self.entries: Dict[TimeOfDay, ScheduleEntry] = {}
        self.override_activities: List[Activity] = []
    
    def add_entry(self, time_of_day: TimeOfDay, activity: Activity) -> None:
        """
        Add an entry to the schedule.
        
        Args:
            time_of_day: The time period for this entry.
            activity: The activity to perform during this time period.
        """
        self.entries[time_of_day] = ScheduleEntry(time_of_day, activity)
    
    def add_override(self, activity: Activity) -> None:
        """
        Add an override activity.
        
        Override activities are checked before regular schedule entries and
        can override the normal schedule if their conditions are met.
        
        Args:
            activity: The override activity.
        """
        self.override_activities.append(activity)
        # Sort overrides by priority (highest first)
        self.override_activities.sort(key=lambda a: a.priority.value, reverse=True)
    
    def get_current_activity(self, time_of_day: TimeOfDay, weather: WeatherType, event: EventType) -> Optional[Activity]:
        """
        Get the activity the NPC should be doing at the current time.
        
        Args:
            time_of_day: The current time of day.
            weather: The current weather.
            event: The current event.
            
        Returns:
            The activity the NPC should be doing, or None if no activity is scheduled.
        """
        # Check override activities first
        for activity in self.override_activities:
            if activity.can_perform(weather, event):
                logging.info(f"[Schedule] NPC {self.npc_name} is doing override activity {activity.name}")
                return activity
        
        # Check regular schedule
        if time_of_day in self.entries:
            entry = self.entries[time_of_day]
            if entry.activity.can_perform(weather, event):
                logging.info(f"[Schedule] NPC {self.npc_name} is doing scheduled activity {entry.activity.name}")
                return entry.activity
        
        # No activity found
        logging.info(f"[Schedule] NPC {self.npc_name} has no activity for {time_of_day.name}")
        return None
    
    def __str__(self) -> str:
        """
        Get a string representation of the schedule.
        
        Returns:
            A string representation of the schedule.
        """
        result = f"Schedule for {self.npc_name}:\n"
        for time_of_day in TimeOfDay:
            if time_of_day in self.entries:
                result += f"  {self.entries[time_of_day]}\n"
        if self.override_activities:
            result += "Override activities:\n"
            for activity in self.override_activities:
                result += f"  {activity}\n"
        return result

class ScheduleManager:
    """
    Manages schedules for all NPCs.
    
    The schedule manager keeps track of the current time, weather, and events,
    and provides methods for NPCs to get their current activities.
    """
    
    def __init__(self):
        """Initialize the schedule manager."""
        self.schedules: Dict[str, Schedule] = {}
        self.current_time_of_day = TimeOfDay.MORNING
        self.current_weather = WeatherType.CLEAR
        self.current_event = EventType.NORMAL
        self.hour_to_time_of_day = {
            5: TimeOfDay.EARLY_MORNING,
            6: TimeOfDay.EARLY_MORNING,
            7: TimeOfDay.EARLY_MORNING,
            8: TimeOfDay.MORNING,
            9: TimeOfDay.MORNING,
            10: TimeOfDay.MORNING,
            11: TimeOfDay.MORNING,
            12: TimeOfDay.NOON,
            13: TimeOfDay.NOON,
            14: TimeOfDay.AFTERNOON,
            15: TimeOfDay.AFTERNOON,
            16: TimeOfDay.AFTERNOON,
            17: TimeOfDay.AFTERNOON,
            18: TimeOfDay.EVENING,
            19: TimeOfDay.EVENING,
            20: TimeOfDay.EVENING,
            21: TimeOfDay.EVENING,
            22: TimeOfDay.NIGHT,
            23: TimeOfDay.NIGHT,
            0: TimeOfDay.NIGHT,
            1: TimeOfDay.NIGHT,
            2: TimeOfDay.NIGHT,
            3: TimeOfDay.NIGHT,
            4: TimeOfDay.NIGHT
        }
    
    def add_schedule(self, npc_id: str, schedule: Schedule) -> None:
        """
        Add a schedule for an NPC.
        
        Args:
            npc_id: The ID of the NPC.
            schedule: The NPC's schedule.
        """
        self.schedules[npc_id] = schedule
    
    def update_time(self, hour: int) -> None:
        """
        Update the current time of day.
        
        Args:
            hour: The current hour (0-23).
        """
        self.current_time_of_day = self.hour_to_time_of_day.get(hour, TimeOfDay.MORNING)
    
    def update_weather(self, weather: WeatherType) -> None:
        """
        Update the current weather.
        
        Args:
            weather: The current weather.
        """
        self.current_weather = weather
    
    def update_event(self, event: EventType) -> None:
        """
        Update the current event.
        
        Args:
            event: The current event.
        """
        self.current_event = event
    
    def get_current_activity(self, npc_id: str) -> Optional[Activity]:
        """
        Get the activity an NPC should be doing at the current time.
        
        Args:
            npc_id: The ID of the NPC.
            
        Returns:
            The activity the NPC should be doing, or None if no activity is scheduled.
        """
        if npc_id not in self.schedules:
            return None
        
        schedule = self.schedules[npc_id]
        return schedule.get_current_activity(
            self.current_time_of_day,
            self.current_weather,
            self.current_event
        )
    
    def generate_random_schedule(self, npc_id: str, home_location: Tuple[int, int], work_location: Tuple[int, int]) -> Schedule:
        """
        Generate a random schedule for an NPC.
        
        Args:
            npc_id: The ID of the NPC.
            home_location: The location of the NPC's home.
            work_location: The location of the NPC's workplace.
            
        Returns:
            A randomly generated schedule.
        """
        schedule = Schedule(npc_id)
        
        # Basic activities
        sleep_activity = Activity("Sleep", home_location, ActivityPriority.HIGH)
        work_activity = Activity("Work", work_location, ActivityPriority.HIGH)
        eat_activity = Activity("Eat", home_location, ActivityPriority.MEDIUM)
        leisure_activity = Activity("Leisure", home_location, ActivityPriority.LOW)
        
        # Add time variance to make NPCs less robotic
        sleep_activity.set_time_variance(0.2)
        work_activity.set_time_variance(0.1)
        eat_activity.set_time_variance(0.3)
        leisure_activity.set_time_variance(0.5)
        
        # Basic schedule
        schedule.add_entry(TimeOfDay.EARLY_MORNING, eat_activity)
        schedule.add_entry(TimeOfDay.MORNING, work_activity)
        schedule.add_entry(TimeOfDay.NOON, eat_activity)
        schedule.add_entry(TimeOfDay.AFTERNOON, work_activity)
        schedule.add_entry(TimeOfDay.EVENING, leisure_activity)
        schedule.add_entry(TimeOfDay.NIGHT, sleep_activity)
        
        # Override activities for special conditions
        shelter_activity = Activity("Seek Shelter", home_location, ActivityPriority.CRITICAL)
        shelter_activity.add_weather_condition(WeatherType.STORMY)
        schedule.add_override(shelter_activity)
        
        flee_activity = Activity("Flee", (0, 0), ActivityPriority.CRITICAL)
        flee_activity.add_event_condition(EventType.RAID)
        schedule.add_override(flee_activity)
        
        festival_activity = Activity("Celebrate", (0, 0), ActivityPriority.HIGH)
        festival_activity.add_event_condition(EventType.FESTIVAL)
        schedule.add_override(festival_activity)
        
        return schedule
