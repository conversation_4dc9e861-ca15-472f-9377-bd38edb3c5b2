"""
Emotional and Social Modeling for Living World RPG.

This module provides systems for modeling NPC emotions, personalities, and social relationships.
NPCs have emotional states that change based on events, personality traits that influence
how they react to events, and relationships with other NPCs that affect their behavior.
"""

from typing import Dict, List, Tuple, Optional, Set, Any
from enum import Enum
import random
import math
import logging

class EmotionType(Enum):
    """Types of emotions that NPCs can experience."""
    JOY = 0
    SADNESS = 1
    ANGER = 2
    FEAR = 3
    DISGUST = 4
    SURPRISE = 5
    TRUST = 6
    ANTICIPATION = 7

class PersonalityTrait(Enum):
    """Personality traits that influence how NPCs react to events."""
    OPENNESS = 0       # Openness to experience
    CONSCIENTIOUSNESS = 1  # Organized, careful, disciplined
    EXTRAVERSION = 2   # Outgoing, energetic, sociable
    AGREEABLENESS = 3  # Friendly, compassionate, cooperative
    NEUROTICISM = 4    # Sensitive, nervous, prone to negative emotions

class RelationshipType(Enum):
    """Types of relationships between NPCs."""
    STRANGER = 0
    ACQUAINTANCE = 1
    FRIEND = 2
    CLOSE_FRIEND = 3
    FAMILY = 4
    ROMANTIC = 5
    RIVAL = 6
    ENEMY = 7

class SocialEventType(Enum):
    """Types of social events that can affect relationships."""
    GREETING = 0
    CONVERSATION = 1
    GIFT = 2
    HELP = 3
    TRADE = 4
    BETRAYAL = 5
    ATTACK = 6
    DEFEND = 7
    COMPLIMENT = 8
    INSULT = 9

class Emotion:
    """
    Represents an emotional state.
    
    Emotions have a type, intensity, and decay rate. The intensity decreases over time
    according to the decay rate.
    """
    
    def __init__(self, emotion_type: EmotionType, intensity: float = 0.0, decay_rate: float = 0.1):
        """
        Initialize an emotion.
        
        Args:
            emotion_type: The type of emotion.
            intensity: The initial intensity of the emotion (0.0 - 1.0).
            decay_rate: The rate at which the emotion decays (0.0 - 1.0).
        """
        self.type = emotion_type
        self.intensity = max(0.0, min(1.0, intensity))
        self.decay_rate = max(0.0, min(1.0, decay_rate))
    
    def update(self, delta_time: float) -> None:
        """
        Update the emotion's intensity based on time passed.
        
        Args:
            delta_time: The time passed since the last update.
        """
        self.intensity = max(0.0, self.intensity - self.decay_rate * delta_time)
    
    def change_intensity(self, delta: float) -> None:
        """
        Change the emotion's intensity.
        
        Args:
            delta: The amount to change the intensity by.
        """
        self.intensity = max(0.0, min(1.0, self.intensity + delta))
    
    def __str__(self) -> str:
        """
        Get a string representation of the emotion.
        
        Returns:
            A string representation of the emotion.
        """
        return f"{self.type.name}: {self.intensity:.2f}"

class Personality:
    """
    Represents an NPC's personality.
    
    Personality is defined by a set of traits, each with a value between 0.0 and 1.0.
    These traits influence how the NPC reacts to events.
    """
    
    def __init__(self):
        """Initialize a personality with random trait values."""
        self.traits: Dict[PersonalityTrait, float] = {}
        for trait in PersonalityTrait:
            self.traits[trait] = random.random()
    
    def set_trait(self, trait: PersonalityTrait, value: float) -> None:
        """
        Set a personality trait value.
        
        Args:
            trait: The trait to set.
            value: The value to set (0.0 - 1.0).
        """
        self.traits[trait] = max(0.0, min(1.0, value))
    
    def get_trait(self, trait: PersonalityTrait) -> float:
        """
        Get a personality trait value.
        
        Args:
            trait: The trait to get.
            
        Returns:
            The value of the trait (0.0 - 1.0).
        """
        return self.traits.get(trait, 0.5)
    
    def get_emotion_modifier(self, emotion_type: EmotionType) -> float:
        """
        Get a modifier for an emotion based on personality traits.
        
        Args:
            emotion_type: The type of emotion.
            
        Returns:
            A modifier for the emotion intensity.
        """
        if emotion_type == EmotionType.JOY:
            return 0.5 + 0.5 * self.get_trait(PersonalityTrait.EXTRAVERSION)
        elif emotion_type == EmotionType.SADNESS:
            return 0.5 + 0.5 * self.get_trait(PersonalityTrait.NEUROTICISM)
        elif emotion_type == EmotionType.ANGER:
            return 0.5 + 0.5 * (1.0 - self.get_trait(PersonalityTrait.AGREEABLENESS))
        elif emotion_type == EmotionType.FEAR:
            return 0.5 + 0.5 * self.get_trait(PersonalityTrait.NEUROTICISM)
        elif emotion_type == EmotionType.DISGUST:
            return 0.5 + 0.5 * (1.0 - self.get_trait(PersonalityTrait.OPENNESS))
        elif emotion_type == EmotionType.SURPRISE:
            return 0.5 + 0.5 * self.get_trait(PersonalityTrait.OPENNESS)
        elif emotion_type == EmotionType.TRUST:
            return 0.5 + 0.5 * self.get_trait(PersonalityTrait.AGREEABLENESS)
        elif emotion_type == EmotionType.ANTICIPATION:
            return 0.5 + 0.5 * self.get_trait(PersonalityTrait.CONSCIENTIOUSNESS)
        else:
            return 1.0
    
    def __str__(self) -> str:
        """
        Get a string representation of the personality.
        
        Returns:
            A string representation of the personality.
        """
        result = "Personality:\n"
        for trait, value in self.traits.items():
            result += f"  {trait.name}: {value:.2f}\n"
        return result

class Relationship:
    """
    Represents a relationship between two NPCs.
    
    Relationships have a type, a score, and a history of interactions.
    """
    
    def __init__(self, other_id: str, relationship_type: RelationshipType = RelationshipType.STRANGER):
        """
        Initialize a relationship.
        
        Args:
            other_id: The ID of the other NPC in the relationship.
            relationship_type: The type of relationship.
        """
        self.other_id = other_id
        self.type = relationship_type
        self.score = 0.0  # -1.0 (hate) to 1.0 (love)
        self.interaction_history: List[Tuple[SocialEventType, float]] = []
    
    def update_score(self, event_type: SocialEventType, value: float) -> None:
        """
        Update the relationship score based on a social event.
        
        Args:
            event_type: The type of social event.
            value: The value to change the score by.
        """
        self.score = max(-1.0, min(1.0, self.score + value))
        self.interaction_history.append((event_type, value))
        
        # Update relationship type based on score
        if self.score < -0.5:
            self.type = RelationshipType.ENEMY
        elif self.score < -0.2:
            self.type = RelationshipType.RIVAL
        elif self.score < 0.2:
            self.type = RelationshipType.STRANGER
        elif self.score < 0.5:
            self.type = RelationshipType.ACQUAINTANCE
        elif self.score < 0.8:
            self.type = RelationshipType.FRIEND
        else:
            self.type = RelationshipType.CLOSE_FRIEND
    
    def get_recent_interactions(self, count: int = 5) -> List[Tuple[SocialEventType, float]]:
        """
        Get the most recent interactions.
        
        Args:
            count: The number of interactions to get.
            
        Returns:
            A list of the most recent interactions.
        """
        return self.interaction_history[-count:] if self.interaction_history else []
    
    def __str__(self) -> str:
        """
        Get a string representation of the relationship.
        
        Returns:
            A string representation of the relationship.
        """
        return f"{self.other_id}: {self.type.name} ({self.score:.2f})"

class EmotionalState:
    """
    Represents an NPC's emotional state.
    
    The emotional state is a collection of emotions with different intensities.
    """
    
    def __init__(self):
        """Initialize an empty emotional state."""
        self.emotions: Dict[EmotionType, Emotion] = {}
        for emotion_type in EmotionType:
            self.emotions[emotion_type] = Emotion(emotion_type)
    
    def update(self, delta_time: float) -> None:
        """
        Update all emotions based on time passed.
        
        Args:
            delta_time: The time passed since the last update.
        """
        for emotion in self.emotions.values():
            emotion.update(delta_time)
    
    def change_emotion(self, emotion_type: EmotionType, delta: float, personality: Personality) -> None:
        """
        Change the intensity of an emotion.
        
        Args:
            emotion_type: The type of emotion to change.
            delta: The amount to change the intensity by.
            personality: The personality to use for modifying the change.
        """
        if emotion_type in self.emotions:
            modifier = personality.get_emotion_modifier(emotion_type)
            self.emotions[emotion_type].change_intensity(delta * modifier)
    
    def get_dominant_emotion(self) -> Optional[Tuple[EmotionType, float]]:
        """
        Get the dominant emotion.
        
        Returns:
            A tuple of the dominant emotion type and its intensity, or None if no emotions are active.
        """
        max_intensity = 0.0
        dominant_emotion = None
        
        for emotion_type, emotion in self.emotions.items():
            if emotion.intensity > max_intensity:
                max_intensity = emotion.intensity
                dominant_emotion = emotion_type
        
        if dominant_emotion is not None:
            return (dominant_emotion, max_intensity)
        else:
            return None
    
    def get_emotion_intensity(self, emotion_type: EmotionType) -> float:
        """
        Get the intensity of an emotion.
        
        Args:
            emotion_type: The type of emotion to get.
            
        Returns:
            The intensity of the emotion (0.0 - 1.0).
        """
        if emotion_type in self.emotions:
            return self.emotions[emotion_type].intensity
        else:
            return 0.0
    
    def __str__(self) -> str:
        """
        Get a string representation of the emotional state.
        
        Returns:
            A string representation of the emotional state.
        """
        result = "Emotions:\n"
        for emotion in self.emotions.values():
            if emotion.intensity > 0.1:  # Only show significant emotions
                result += f"  {emotion}\n"
        return result

class SocialNetwork:
    """
    Represents an NPC's social network.
    
    The social network is a collection of relationships with other NPCs.
    """
    
    def __init__(self, npc_id: str):
        """
        Initialize a social network.
        
        Args:
            npc_id: The ID of the NPC this network belongs to.
        """
        self.npc_id = npc_id
        self.relationships: Dict[str, Relationship] = {}
    
    def add_relationship(self, other_id: str, relationship_type: RelationshipType = RelationshipType.STRANGER) -> None:
        """
        Add a relationship to the network.
        
        Args:
            other_id: The ID of the other NPC in the relationship.
            relationship_type: The type of relationship.
        """
        if other_id not in self.relationships:
            self.relationships[other_id] = Relationship(other_id, relationship_type)
    
    def update_relationship(self, other_id: str, event_type: SocialEventType, value: float) -> None:
        """
        Update a relationship based on a social event.
        
        Args:
            other_id: The ID of the other NPC in the relationship.
            event_type: The type of social event.
            value: The value to change the relationship score by.
        """
        if other_id not in self.relationships:
            self.add_relationship(other_id)
        
        self.relationships[other_id].update_score(event_type, value)
    
    def get_relationship(self, other_id: str) -> Optional[Relationship]:
        """
        Get a relationship from the network.
        
        Args:
            other_id: The ID of the other NPC in the relationship.
            
        Returns:
            The relationship, or None if it doesn't exist.
        """
        return self.relationships.get(other_id)
    
    def get_relationships_by_type(self, relationship_type: RelationshipType) -> List[Relationship]:
        """
        Get all relationships of a specific type.
        
        Args:
            relationship_type: The type of relationship to get.
            
        Returns:
            A list of relationships of the specified type.
        """
        return [r for r in self.relationships.values() if r.type == relationship_type]
    
    def get_closest_relationships(self, count: int = 5) -> List[Relationship]:
        """
        Get the closest relationships.
        
        Args:
            count: The number of relationships to get.
            
        Returns:
            A list of the closest relationships.
        """
        sorted_relationships = sorted(self.relationships.values(), key=lambda r: r.score, reverse=True)
        return sorted_relationships[:count]
    
    def __str__(self) -> str:
        """
        Get a string representation of the social network.
        
        Returns:
            A string representation of the social network.
        """
        result = f"Social Network for {self.npc_id}:\n"
        for relationship in self.relationships.values():
            result += f"  {relationship}\n"
        return result

class SocialEventManager:
    """
    Manages social events between NPCs.
    
    The social event manager processes social events and updates the emotional states
    and social networks of the NPCs involved.
    """
    
    def __init__(self):
        """Initialize the social event manager."""
        self.event_values: Dict[SocialEventType, float] = {
            SocialEventType.GREETING: 0.01,
            SocialEventType.CONVERSATION: 0.02,
            SocialEventType.GIFT: 0.1,
            SocialEventType.HELP: 0.2,
            SocialEventType.TRADE: 0.05,
            SocialEventType.BETRAYAL: -0.3,
            SocialEventType.ATTACK: -0.5,
            SocialEventType.DEFEND: 0.3,
            SocialEventType.COMPLIMENT: 0.1,
            SocialEventType.INSULT: -0.2
        }
        
        self.event_emotions: Dict[SocialEventType, List[Tuple[EmotionType, float]]] = {
            SocialEventType.GREETING: [(EmotionType.JOY, 0.01)],
            SocialEventType.CONVERSATION: [(EmotionType.JOY, 0.02)],
            SocialEventType.GIFT: [(EmotionType.JOY, 0.1), (EmotionType.SURPRISE, 0.05)],
            SocialEventType.HELP: [(EmotionType.JOY, 0.1), (EmotionType.TRUST, 0.2)],
            SocialEventType.TRADE: [(EmotionType.TRUST, 0.05)],
            SocialEventType.BETRAYAL: [(EmotionType.ANGER, 0.3), (EmotionType.SADNESS, 0.2)],
            SocialEventType.ATTACK: [(EmotionType.FEAR, 0.5), (EmotionType.ANGER, 0.3)],
            SocialEventType.DEFEND: [(EmotionType.TRUST, 0.3), (EmotionType.JOY, 0.1)],
            SocialEventType.COMPLIMENT: [(EmotionType.JOY, 0.1)],
            SocialEventType.INSULT: [(EmotionType.ANGER, 0.2), (EmotionType.SADNESS, 0.1)]
        }
    
    def process_event(self, event_type: SocialEventType, npc1_id: str, npc2_id: str,
                     npc1_network: SocialNetwork, npc2_network: SocialNetwork,
                     npc1_emotions: EmotionalState, npc2_emotions: EmotionalState,
                     npc1_personality: Personality, npc2_personality: Personality) -> None:
        """
        Process a social event between two NPCs.
        
        Args:
            event_type: The type of social event.
            npc1_id: The ID of the first NPC.
            npc2_id: The ID of the second NPC.
            npc1_network: The social network of the first NPC.
            npc2_network: The social network of the second NPC.
            npc1_emotions: The emotional state of the first NPC.
            npc2_emotions: The emotional state of the second NPC.
            npc1_personality: The personality of the first NPC.
            npc2_personality: The personality of the second NPC.
        """
        # Get the base value for this event type
        base_value = self.event_values.get(event_type, 0.0)
        
        # Update relationships
        npc1_network.update_relationship(npc2_id, event_type, base_value)
        npc2_network.update_relationship(npc1_id, event_type, base_value)
        
        # Update emotions
        if event_type in self.event_emotions:
            for emotion_type, intensity in self.event_emotions[event_type]:
                npc1_emotions.change_emotion(emotion_type, intensity, npc1_personality)
                npc2_emotions.change_emotion(emotion_type, intensity, npc2_personality)
        
        logging.info(f"[Social] Processed {event_type.name} event between {npc1_id} and {npc2_id}")

class SocialComponent:
    """
    Component that stores an NPC's social and emotional state.
    
    This component combines personality, emotional state, and social network
    into a single component that can be attached to an NPC entity.
    """
    
    def __init__(self, npc_id: str):
        """
        Initialize a social component.
        
        Args:
            npc_id: The ID of the NPC this component belongs to.
        """
        self.npc_id = npc_id
        self.personality = Personality()
        self.emotional_state = EmotionalState()
        self.social_network = SocialNetwork(npc_id)
    
    def update(self, delta_time: float) -> None:
        """
        Update the social component.
        
        Args:
            delta_time: The time passed since the last update.
        """
        self.emotional_state.update(delta_time)
    
    def get_decision_modifiers(self) -> Dict[str, float]:
        """
        Get modifiers for decision making based on emotional state.
        
        Returns:
            A dictionary of decision modifiers.
        """
        modifiers = {}
        
        # Personality-based modifiers
        modifiers["risk_taking"] = 0.5 + 0.5 * (
            self.personality.get_trait(PersonalityTrait.OPENNESS) -
            self.personality.get_trait(PersonalityTrait.CONSCIENTIOUSNESS)
        )
        
        modifiers["sociability"] = 0.5 + 0.5 * (
            self.personality.get_trait(PersonalityTrait.EXTRAVERSION) +
            self.personality.get_trait(PersonalityTrait.AGREEABLENESS)
        ) / 2.0
        
        modifiers["aggression"] = 0.5 + 0.5 * (
            self.personality.get_trait(PersonalityTrait.NEUROTICISM) -
            self.personality.get_trait(PersonalityTrait.AGREEABLENESS)
        )
        
        # Emotion-based modifiers
        fear = self.emotional_state.get_emotion_intensity(EmotionType.FEAR)
        anger = self.emotional_state.get_emotion_intensity(EmotionType.ANGER)
        joy = self.emotional_state.get_emotion_intensity(EmotionType.JOY)
        
        modifiers["flee_tendency"] = fear
        modifiers["attack_tendency"] = anger
        modifiers["help_tendency"] = joy
        
        return modifiers
    
    def __str__(self) -> str:
        """
        Get a string representation of the social component.
        
        Returns:
            A string representation of the social component.
        """
        result = f"Social Component for {self.npc_id}:\n"
        result += str(self.personality)
        result += str(self.emotional_state)
        result += str(self.social_network)
        return result
