# File: src/core/ecs.py
"""
Core ECS framework for Living World RPG.
Provides the Entity class, the abstract System class, and the ECSWorld manager.
"""

import uuid
import logging
from typing import List, Dict, Type, Any, Set, Optional
from collections import defaultdict
from utils.elastic_dict_adapter import ElasticDictAdapter
from utils.elastic_hash_table import ElasticHashTable

logging.basicConfig(level=logging.INFO)


class Entity:
    """Represents a game entity with a unique ID and a collection of components."""
    def __init__(self) -> None:
        self.id = uuid.uuid4()
        self.components = ElasticDictAdapter(size=16)  # Most entities have few components

    def add_component(self, component: Any) -> None:
        """
        Attaches a component to the entity.
        Components are stored by their class type.
        """
        self.components[type(component)] = component

    def get_component(self, comp_type: Type) -> Any:
        """
        Retrieves the component of the given type, if present.
        """
        return self.components.get(comp_type)

    def remove_component(self, comp_type: Type) -> None:
        """Removes the component of the given type."""
        self.components.pop(comp_type, None)


class System:
    """
    Base class for all systems.
    Subclasses must implement the update() method.
    """
    def __init__(self) -> None:
        self.entities: List[Entity] = []
        self.world: "ECSWorld" = None  # Will be set when the system is added to the world

    def required_components(self) -> List[Type]:
        """
        Returns a list of component types required for an entity to be processed by this system.
        """
        return []

    def register_entity(self, entity: Entity) -> None:
        """
        Registers an entity if it contains all required components.
        """
        if all(entity.get_component(comp) is not None for comp in self.required_components()):
            self.entities.append(entity)

    def unregister_entity(self, entity: Entity) -> None:
        """Unregisters an entity from this system."""
        if entity in self.entities:
            self.entities.remove(entity)

    def update(self, dt: float, **kwargs) -> None:
        """Processes all entities. Must be overridden by subclasses."""
        raise NotImplementedError("System must implement update(dt, **kwargs)")


class ECSWorld:
    """
    Manages all entities and systems.
    Also tracks global game time.
    Enhanced with performance optimizations:
    - Entity pooling for frequent creation/destruction
    - Component indexing for fast queries
    - System priority management
    """
    def __init__(self) -> None:
        self.entities: List[Entity] = []
        self.systems: List[System] = []
        self.day_time: float = 0.0  # In-game time (e.g., hours)
        self.game_days: int = 0     # Total number of days elapsed

        # Performance optimizations
        self._entity_pool: List[Entity] = []  # Pool for reusing entities
        self._component_index: Dict[Type, Set[Entity]] = defaultdict(set)  # Fast component lookup
        self._query_cache: ElasticHashTable = ElasticHashTable(size=64)  # Cache for frequent queries
        self._system_priorities: Dict[System, int] = {}  # System execution order
        self._dirty_entities: Set[Entity] = set()  # Entities that need re-indexing

    def create_entity(self) -> Entity:
        """Creates and registers a new entity, using pooling for performance."""
        # Try to reuse an entity from the pool
        if self._entity_pool:
            entity = self._entity_pool.pop()
            # Clear any existing components
            entity.components.clear()
            # Generate new ID
            entity.id = uuid.uuid4()
        else:
            entity = Entity()

        self.entities.append(entity)
        for system in self.systems:
            system.register_entity(entity)
        return entity

    def destroy_entity(self, entity: Entity) -> None:
        """Destroys an entity and returns it to the pool for reuse."""
        if entity in self.entities:
            # Remove from all systems
            for system in self.systems:
                system.unregister_entity(entity)

            # Remove from component index
            for comp_type in list(entity.components.keys()):
                self._component_index[comp_type].discard(entity)

            # Remove from entities list
            self.entities.remove(entity)

            # Add to pool for reuse (limit pool size to prevent memory bloat)
            if len(self._entity_pool) < 100:
                self._entity_pool.append(entity)

    def add_component_to_entity(self, entity: Entity, component: Any) -> None:
        """Adds a component to an entity and updates the index."""
        comp_type = type(component)
        entity.add_component(component)
        self._component_index[comp_type].add(entity)
        self._dirty_entities.add(entity)

        # Re-register with systems that might now be interested
        for system in self.systems:
            if all(entity.get_component(ct) for ct in system.required_components()):
                if entity not in system.entities:
                    system.entities.append(entity)

    def remove_component_from_entity(self, entity: Entity, comp_type: Type) -> None:
        """Removes a component from an entity and updates the index."""
        if entity.get_component(comp_type):
            entity.remove_component(comp_type)
            self._component_index[comp_type].discard(entity)
            self._dirty_entities.add(entity)

            # Unregister from systems that no longer apply
            for system in self.systems:
                if not all(entity.get_component(ct) for ct in system.required_components()):
                    system.unregister_entity(entity)

    def add_system(self, system: System, priority: int = 0) -> None:
        """Adds a system with optional priority and registers all existing entities with it."""
        system.world = self
        self.systems.append(system)
        self._system_priorities[system] = priority

        # Sort systems by priority (higher priority runs first)
        self.systems.sort(key=lambda s: self._system_priorities.get(s, 0), reverse=True)

        for entity in self.entities:
            system.register_entity(entity)

    def update(self, dt: float, **kwargs) -> None:
        """
        Advances the world by dt and updates all systems.
        Also increments game days based on day_time.
        Enhanced with performance monitoring.
        """
        self.day_time += dt
        if self.day_time >= 24.0:
            self.day_time -= 24.0
            self.game_days += 1

        # Process any dirty entities before system updates
        self._process_dirty_entities()

        # Update systems in priority order
        for system in self.systems:
            try:
                system.update(dt, **kwargs)
            except Exception as e:
                logging.error(f"Error updating system {type(system).__name__}: {e}")

    def get_entities_with(self, *component_types) -> List[Any]:
        """
        Returns a list of tuples (entity, components_tuple) for entities that have all the specified components.
        Enhanced with caching for performance.
        """
        # Create cache key from component types
        cache_key = tuple(sorted(ct.__name__ for ct in component_types))

        # Check cache first
        cached_result = self._query_cache.get(cache_key)
        if cached_result is not None:
            # Validate cached entities are still valid
            valid_results = []
            for entity, comps in cached_result:
                if entity in self.entities and all(entity.get_component(ct) for ct in component_types):
                    valid_results.append((entity, comps))
            if len(valid_results) == len(cached_result):
                return valid_results

        # Perform query using component index for better performance
        if component_types:
            # Start with entities that have the first component type
            candidate_entities = self._component_index.get(component_types[0], set())

            # Filter by remaining component types
            for comp_type in component_types[1:]:
                candidate_entities = candidate_entities.intersection(
                    self._component_index.get(comp_type, set())
                )
        else:
            candidate_entities = set(self.entities)

        # Build results
        results = []
        for entity in candidate_entities:
            comps = [entity.get_component(ct) for ct in component_types]
            if all(comps):
                results.append((entity, tuple(comps)))

        # Cache the result
        self._query_cache[cache_key] = results
        return results

    def get_player_entity(self) -> Any:
        """
        Returns the first entity that has a PlayerComponent.
        """
        from core.components import PlayerComponent
        for entity in self.entities:
            if entity.get_component(PlayerComponent):
                return entity
        return None

    @property
    def total_days(self) -> float:
        """Returns the total number of days elapsed, including fractional days."""
        return self.game_days + (self.day_time / 24.0)

    def _process_dirty_entities(self) -> None:
        """Process entities that need re-indexing due to component changes."""
        if not self._dirty_entities:
            return

        for entity in self._dirty_entities:
            # Update component index
            for comp_type, component in entity.components.items():
                self._component_index[comp_type].add(entity)

        # Clear query cache since entity components have changed
        self._query_cache.clear()
        self._dirty_entities.clear()

    def get_entities_with_component(self, component_type: Type) -> Set[Entity]:
        """Fast lookup for entities with a specific component type."""
        return self._component_index.get(component_type, set()).copy()

    def get_performance_stats(self) -> Dict[str, Any]:
        """Returns performance statistics for monitoring."""
        return {
            'total_entities': len(self.entities),
            'pooled_entities': len(self._entity_pool),
            'total_systems': len(self.systems),
            'cached_queries': len(self._query_cache),
            'dirty_entities': len(self._dirty_entities),
            'component_types': len(self._component_index),
            'memory_efficiency': len(self._entity_pool) / max(1, len(self.entities)) * 100
        }
