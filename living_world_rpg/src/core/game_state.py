from typing import Optional, Dict, Any, Tuple, List, Callable, Set
import logging
import time
import json
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum

# Color constants
COLOR_WHITE = (255, 255, 255)
COLOR_RED = (255, 0, 0)
COLOR_GREEN = (0, 255, 0)
COLOR_BLUE = (0, 0, 255)
COLOR_YELLOW = (255, 255, 0)

class GameStateEvent(Enum):
    """Events that can be triggered by state changes."""
    STATE_CHANGED = "state_changed"
    PLAYER_MOVED = "player_moved"
    INVENTORY_CHANGED = "inventory_changed"
    WORLD_GENERATED = "world_generated"
    GAME_STARTED = "game_started"
    GAME_PAUSED = "game_paused"

@dataclass
class FeedbackMessage:
    """Represents a feedback message with metadata."""
    text: str
    timestamp: float
    duration: float
    color: Tuple[int, int, int]
    priority: int = 0  # Higher priority messages stay longer

    @property
    def is_expired(self) -> bool:
        """Check if the message has expired."""
        return time.time() > self.timestamp + self.duration

class GameState:
    """Enhanced unified game state management system.

    This class manages all shared state across different game screens and systems.
    It includes game settings, player state, UI feedback mechanisms, state persistence,
    change notifications, and comprehensive logging.

    Features:
    - State change notifications via callbacks
    - Message priority system
    - State validation and debugging
    - Persistence capabilities
    - Comprehensive logging
    """

    def __init__(self) -> None:
        """Initialize the game state with default values."""
        try:
            # Core game state
            self._current_state: str = "start_menu"
            self._previous_state: str = ""
            self.started_game: bool = False
            self.is_fullscreen: bool = False
            self.debug_overlay: bool = False
            self.is_paused: bool = False

            # World generation
            self.selected_seed: str = ""
            self.preview_map: Optional[Dict[str, Any]] = None
            self.player_biome_name: Optional[str] = None
            self.selected_biome_index: int = 0

            # Player state
            self.player_race_name: Optional[str] = None
            self.selected_race_index: int = 0
            self._player_x: float = 0.0
            self._player_y: float = 0.0
            self.companion_x: float = 0.0
            self.companion_y: float = 1.0
            self.follow_mode: bool = False
            self.last_attack_time: float = 0.0

            # World state (merged from main.py version)
            self.settlement_pos: Optional[Tuple[float, float]] = None

            # Inventory and resources
            self.inventory_open: bool = False
            self.npc_ore_count: int = 0
            self.last_ore_gather: float = time.time()

            # Enhanced UI feedback system
            self.feedback_messages: List[FeedbackMessage] = []
            self.message_duration: float = 2.0
            self.default_message_color: Tuple[int, int, int] = COLOR_WHITE
            self.error_message_color: Tuple[int, int, int] = COLOR_RED
            self.success_message_color: Tuple[int, int, int] = COLOR_GREEN
            self.warning_message_color: Tuple[int, int, int] = COLOR_YELLOW
            self.info_message_color: Tuple[int, int, int] = COLOR_BLUE

            # State change notification system
            self._state_change_callbacks: Dict[GameStateEvent, List[Callable]] = {
                event: [] for event in GameStateEvent
            }

            # State validation and debugging
            self._state_history: List[Tuple[str, float]] = []
            self._max_history_size: int = 100
            self._validation_enabled: bool = True

            # Performance tracking
            self._state_change_count: int = 0
            self._last_state_change_time: float = time.time()

            logging.info("[GameState] Enhanced GameState initialized successfully")

        except Exception as e:
            logging.error(f"Error initializing GameState: {str(e)}")
            raise

    # Properties for controlled access to critical state
    @property
    def current_state(self) -> str:
        """Get the current game state."""
        return self._current_state

    @current_state.setter
    def current_state(self, new_state: str) -> None:
        """Set the current game state with validation and notifications."""
        if self._validation_enabled and not self._validate_state_transition(new_state):
            logging.warning(f"[GameState] Invalid state transition: {self._current_state} -> {new_state}")
            return

        old_state = self._current_state
        self._previous_state = old_state
        self._current_state = new_state
        self._state_change_count += 1
        self._last_state_change_time = time.time()

        # Add to history
        self._state_history.append((new_state, time.time()))
        if len(self._state_history) > self._max_history_size:
            self._state_history.pop(0)

        # Trigger callbacks
        self._trigger_event(GameStateEvent.STATE_CHANGED, {
            'old_state': old_state,
            'new_state': new_state,
            'timestamp': time.time()
        })

        logging.info(f"[GameState] State changed: {old_state} -> {new_state}")

    @property
    def player_x(self) -> float:
        """Get player X position."""
        return self._player_x

    @player_x.setter
    def player_x(self, value: float) -> None:
        """Set player X position with movement notification."""
        old_x = self._player_x
        self._player_x = value
        if abs(old_x - value) > 0.1:  # Only trigger for significant movement
            self._trigger_event(GameStateEvent.PLAYER_MOVED, {
                'old_pos': (old_x, self._player_y),
                'new_pos': (value, self._player_y)
            })

    @property
    def player_y(self) -> float:
        """Get player Y position."""
        return self._player_y

    @player_y.setter
    def player_y(self, value: float) -> None:
        """Set player Y position with movement notification."""
        old_y = self._player_y
        self._player_y = value
        if abs(old_y - value) > 0.1:  # Only trigger for significant movement
            self._trigger_event(GameStateEvent.PLAYER_MOVED, {
                'old_pos': (self._player_x, old_y),
                'new_pos': (self._player_x, value)
            })

    def show_feedback_message(self,
                           message: str,
                           color: Optional[Tuple[int, int, int]] = None,
                           duration: Optional[float] = None,
                           priority: int = 0) -> None:
        """Add a new feedback message with enhanced features.

        Args:
            message: The text message to display
            color: RGB tuple for the message color. Defaults to default message color.
            duration: Duration in seconds to display the message. Defaults to message_duration.
            priority: Message priority (higher = more important, stays longer)
        """
        try:
            if color is None:
                color = self.default_message_color
            if duration is None:
                duration = self.message_duration

            # Adjust duration based on priority
            if priority > 0:
                duration *= (1 + priority * 0.5)

            feedback_msg = FeedbackMessage(
                text=message,
                timestamp=time.time(),
                duration=duration,
                color=color,
                priority=priority
            )

            self.feedback_messages.append(feedback_msg)

            # Clean up expired messages
            self._cleanup_expired_messages()

            logging.debug(f"[GameState] Added feedback message: {message} (priority: {priority})")

        except Exception as e:
            logging.error(f"Error adding feedback message: {str(e)}")

    def show_error_message(self, message: str, priority: int = 1) -> None:
        """Show an error message with red color and higher priority."""
        self.show_feedback_message(message, self.error_message_color, priority=priority)

    def show_success_message(self, message: str, priority: int = 0) -> None:
        """Show a success message with green color."""
        self.show_feedback_message(message, self.success_message_color, priority=priority)

    def show_warning_message(self, message: str, priority: int = 1) -> None:
        """Show a warning message with yellow color."""
        self.show_feedback_message(message, self.warning_message_color, priority=priority)

    def show_info_message(self, message: str, priority: int = 0) -> None:
        """Show an info message with blue color."""
        self.show_feedback_message(message, self.info_message_color, priority=priority)

    # Event system methods
    def register_callback(self, event: GameStateEvent, callback: Callable) -> None:
        """Register a callback for a specific game state event.

        Args:
            event: The event type to listen for
            callback: Function to call when event occurs
        """
        if event in self._state_change_callbacks:
            self._state_change_callbacks[event].append(callback)
            logging.debug(f"[GameState] Registered callback for {event.value}")
        else:
            logging.warning(f"[GameState] Unknown event type: {event}")

    def unregister_callback(self, event: GameStateEvent, callback: Callable) -> None:
        """Unregister a callback for a specific game state event.

        Args:
            event: The event type to stop listening for
            callback: Function to remove from callbacks
        """
        if event in self._state_change_callbacks:
            try:
                self._state_change_callbacks[event].remove(callback)
                logging.debug(f"[GameState] Unregistered callback for {event.value}")
            except ValueError:
                logging.warning(f"[GameState] Callback not found for {event.value}")

    def _trigger_event(self, event: GameStateEvent, data: Dict[str, Any]) -> None:
        """Trigger all callbacks for a specific event.

        Args:
            event: The event type that occurred
            data: Event data to pass to callbacks
        """
        try:
            for callback in self._state_change_callbacks.get(event, []):
                try:
                    callback(data)
                except Exception as e:
                    logging.error(f"[GameState] Error in event callback for {event.value}: {e}")
        except Exception as e:
            logging.error(f"[GameState] Error triggering event {event.value}: {e}")

    # Utility methods
    def _cleanup_expired_messages(self) -> None:
        """Remove expired feedback messages."""
        self.feedback_messages = [msg for msg in self.feedback_messages if not msg.is_expired]

    def get_active_messages(self) -> List[FeedbackMessage]:
        """Get all non-expired feedback messages sorted by priority."""
        self._cleanup_expired_messages()
        return sorted(self.feedback_messages, key=lambda x: x.priority, reverse=True)

    def clear_messages(self) -> None:
        """Clear all feedback messages."""
        self.feedback_messages.clear()
        logging.debug("[GameState] Cleared all feedback messages")

    def _validate_state_transition(self, new_state: str) -> bool:
        """Validate if a state transition is allowed.

        Args:
            new_state: The state to transition to

        Returns:
            True if transition is valid, False otherwise
        """
        # Define valid state transitions
        valid_transitions = {
            "start_menu": ["character_selection", "quit"],
            "character_selection": ["biome_selection", "start_menu"],
            "biome_selection": ["seed_selection", "character_selection"],
            "seed_selection": ["loading", "biome_selection"],
            "loading": ["gameplay"],
            "gameplay": ["pause", "inventory", "crafting", "start_menu"],
            "pause": ["gameplay", "start_menu"],
            "inventory": ["gameplay"],
            "crafting": ["gameplay"],
            "quit": []
        }

        current_valid = valid_transitions.get(self._current_state, [])
        return new_state in current_valid or new_state == "quit"  # Always allow quit

    # State persistence methods
    def save_state(self, filepath: Optional[str] = None) -> bool:
        """Save the current game state to a file.

        Args:
            filepath: Path to save file. If None, uses default location.

        Returns:
            True if save successful, False otherwise
        """
        try:
            if filepath is None:
                filepath = "game_state.json"

            state_data = {
                "current_state": self._current_state,
                "started_game": self.started_game,
                "selected_seed": self.selected_seed,
                "player_race_name": self.player_race_name,
                "player_biome_name": self.player_biome_name,
                "player_x": self._player_x,
                "player_y": self._player_y,
                "settlement_pos": self.settlement_pos,
                "npc_ore_count": self.npc_ore_count,
                "timestamp": time.time()
            }

            with open(filepath, 'w') as f:
                json.dump(state_data, f, indent=2)

            logging.info(f"[GameState] State saved to {filepath}")
            return True

        except Exception as e:
            logging.error(f"[GameState] Error saving state: {e}")
            return False

    def load_state(self, filepath: Optional[str] = None) -> bool:
        """Load game state from a file.

        Args:
            filepath: Path to load file. If None, uses default location.

        Returns:
            True if load successful, False otherwise
        """
        try:
            if filepath is None:
                filepath = "game_state.json"

            with open(filepath, 'r') as f:
                state_data = json.load(f)

            # Restore state (bypass validation during load)
            validation_was_enabled = self._validation_enabled
            self._validation_enabled = False

            self._current_state = state_data.get("current_state", "start_menu")
            self.started_game = state_data.get("started_game", False)
            self.selected_seed = state_data.get("selected_seed", "")
            self.player_race_name = state_data.get("player_race_name")
            self.player_biome_name = state_data.get("player_biome_name")
            self._player_x = state_data.get("player_x", 0.0)
            self._player_y = state_data.get("player_y", 0.0)

            # Handle settlement_pos conversion from list to tuple
            settlement_data = state_data.get("settlement_pos")
            if settlement_data and isinstance(settlement_data, list) and len(settlement_data) == 2:
                self.settlement_pos = tuple(settlement_data)
            else:
                self.settlement_pos = settlement_data

            self.npc_ore_count = state_data.get("npc_ore_count", 0)

            # Restore validation setting
            self._validation_enabled = validation_was_enabled

            logging.info(f"[GameState] State loaded from {filepath}")
            return True

        except Exception as e:
            logging.error(f"[GameState] Error loading state: {e}")
            return False

    # Debug and monitoring methods
    def get_state_info(self) -> Dict[str, Any]:
        """Get comprehensive state information for debugging.

        Returns:
            Dictionary containing state information
        """
        return {
            "current_state": self._current_state,
            "previous_state": self._previous_state,
            "state_change_count": self._state_change_count,
            "last_state_change": self._last_state_change_time,
            "active_messages": len(self.get_active_messages()),
            "total_messages": len(self.feedback_messages),
            "registered_callbacks": {
                event.value: len(callbacks)
                for event, callbacks in self._state_change_callbacks.items()
            },
            "player_position": (self._player_x, self._player_y),
            "started_game": self.started_game,
            "is_paused": self.is_paused
        }

    def get_state_history(self) -> List[Tuple[str, float]]:
        """Get the state change history.

        Returns:
            List of (state, timestamp) tuples
        """
        return self._state_history.copy()

    def enable_validation(self, enabled: bool = True) -> None:
        """Enable or disable state transition validation.

        Args:
            enabled: Whether to enable validation
        """
        self._validation_enabled = enabled
        logging.info(f"[GameState] State validation {'enabled' if enabled else 'disabled'}")

    def reset_state(self) -> None:
        """Reset the game state to initial values."""
        try:
            self.__init__()
            logging.info("[GameState] State reset to initial values")
        except Exception as e:
            logging.error(f"[GameState] Error resetting state: {e}")
            raise
