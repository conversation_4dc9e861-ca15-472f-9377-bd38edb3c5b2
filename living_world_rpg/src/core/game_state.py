from typing import Optional, Dict, Any, Tu<PERSON>, List
import logging
import time

# Color constants
COLOR_WHITE = (255, 255, 255)
COLOR_RED = (255, 0, 0)
COLOR_GREEN = (0, 255, 0)

class GameState:
    """Holds global state for the game.

    This class manages all shared state across different game screens and systems.
    It includes game settings, player state, and UI feedback mechanisms.
    """
    
    def __init__(self) -> None:
        """Initialize the game state with default values."""
        try:
            # Game state
            self.current_state: str = "start_menu"
            self.started_game: bool = False
            self.is_fullscreen: bool = False
            self.debug_overlay: bool = False

            # World generation
            self.selected_seed: str = ""
            self.preview_map: Optional[Dict[str, Any]] = None
            self.player_biome_name: Optional[str] = None
            self.selected_biome_index: int = 0

            # Player state
            self.player_race_name: Optional[str] = None
            self.selected_race_index: int = 0
            self.player_x: float = 0.0
            self.player_y: float = 0.0
            self.companion_x: float = 0.0
            self.companion_y: float = 1.0
            self.follow_mode: bool = False
            self.last_attack_time: float = 0.0

            # Inventory and resources
            self.inventory_open: bool = False
            self.npc_ore_count: int = 0
            self.last_ore_gather: float = time.time()

            # UI feedback
            self.feedback_messages: List[Tuple[str, float, float, Tuple[int, int, int]]] = []
            self.message_duration: float = 2.0  # Message display time in seconds
            self.default_message_color: Tuple[int, int, int] = COLOR_WHITE
            self.error_message_color: Tuple[int, int, int] = COLOR_RED
            self.success_message_color: Tuple[int, int, int] = COLOR_GREEN

        except Exception as e:
            logging.error(f"Error initializing GameState: {str(e)}")
            raise

    def show_feedback_message(self, 
                           message: str, 
                           color: Tuple[int, int, int] = None,
                           duration: float = None) -> None:
        """Add a new feedback message that will be displayed on the gameplay screen.

        Args:
            message: The text message to display
            color: RGB tuple for the message color. Defaults to default message color.
            duration: Duration in seconds to display the message. Defaults to message_duration.
        """
        if color is None:
            color = self.default_message_color
        if duration is None:
            duration = self.message_duration
        self.feedback_messages.append((message, time.time(), duration, color))

    def show_error_message(self, message: str) -> None:
        """Show an error message with red color."""
        self.show_feedback_message(message, self.error_message_color)

    def show_success_message(self, message: str) -> None:
        """Show a success message with green color."""
        self.show_feedback_message(message, self.success_message_color)
