# File: src/factories/creature_factory.py
"""
Creature Factory for Living World RPG

Creates different types of creatures with appropriate components for combat.
"""

import random
from typing import Dict, List, Optional, Any, Tuple

from core.entity import Entity
from core.entity_manager import EntityManager
from core.components import (
    PositionComponent, CreatureComponent, CombatComponent,
    AIComponent, WeaponComponent, ArmorComponent
)


class CreatureFactory:
    """
    Factory for creating different types of creatures.
    """
    def __init__(self, entity_manager: EntityManager):
        self.entity_manager = entity_manager

    def create_creature(self, creature_type: str, x: float, y: float, **kwargs) -> Entity:
        """
        Create a creature of the specified type.

        Args:
            creature_type: Type of creature to create
            x: X position
            y: Y position
            **kwargs: Additional parameters

        Returns:
            The created creature entity
        """
        if creature_type == "wolf":
            return self._create_wolf(x, y, **kwargs)
        elif creature_type == "bear":
            return self._create_bear(x, y, **kwargs)
        elif creature_type == "bandit":
            return self._create_bandit(x, y, **kwargs)
        elif creature_type == "guard":
            return self._create_guard(x, y, **kwargs)
        elif creature_type == "deer":
            return self._create_deer(x, y, **kwargs)
        elif creature_type == "rabbit":
            return self._create_rabbit(x, y, **kwargs)
        else:
            # Default to a generic creature
            return self._create_generic_creature(creature_type, x, y, **kwargs)

    def _create_wolf(self, x: float, y: float, **kwargs) -> Entity:
        """
        Create a wolf creature.

        Args:
            x: X position
            y: Y position
            **kwargs: Additional parameters

        Returns:
            The created wolf entity
        """
        # Create the entity
        entity = self.entity_manager.create_entity()

        # Add position component
        entity.add_component(PositionComponent(x, y))

        # Add creature component
        creature_name = kwargs.get("name", "Wolf")
        threat_level = kwargs.get("threat_level", "Medium")
        behavior = kwargs.get("behavior", "Aggressive")
        loot = kwargs.get("loot", ["Wolf Pelt", "Wolf Fang"])
        hit_points = kwargs.get("hit_points", 30)

        entity.add_component(CreatureComponent(
            creature_name=creature_name,
            threat_level=threat_level,
            behavior=behavior,
            loot=loot,
            hit_points=hit_points
        ))

        # Add combat component
        attack = kwargs.get("attack", 15)
        defense = kwargs.get("defense", 5)
        health = kwargs.get("health", 30)
        stamina = kwargs.get("stamina", 50)

        combat_comp = CombatComponent(
            attack=attack,
            defense=defense,
            health=health,
            max_health=health,
            stamina=stamina,
            max_stamina=stamina
        )
        combat_comp.is_hostile = True
        combat_comp.critical_chance = 0.1  # 10% critical chance
        combat_comp.dodge_chance = 0.15    # 15% dodge chance
        entity.add_component(combat_comp)

        # Add AI component
        ai_comp = AIComponent(behavior_type="aggressive")
        entity.add_component(ai_comp)

        # Add natural weapon component (claws/teeth)
        weapon_comp = WeaponComponent(
            weapon_type="natural",
            base_damage=8,
            damage_type="physical",
            attack_speed=1.2,
            range=1.0,
            critical_bonus=0.05,
            special_effects={
                "bleeding": {
                    "chance": 0.2,
                    "duration": 5.0,
                    "modifiers": {
                        "health_regen": -1  # Lose 1 health per second
                    }
                }
            }
        )
        entity.add_component(weapon_comp)

        return entity

    def _create_bear(self, x: float, y: float, **kwargs) -> Entity:
        """
        Create a bear creature.

        Args:
            x: X position
            y: Y position
            **kwargs: Additional parameters

        Returns:
            The created bear entity
        """
        # Create the entity
        entity = self.entity_manager.create_entity()

        # Add position component
        entity.add_component(PositionComponent(x, y))

        # Add creature component
        creature_name = kwargs.get("name", "Bear")
        threat_level = kwargs.get("threat_level", "High")
        behavior = kwargs.get("behavior", "Territorial")
        loot = kwargs.get("loot", ["Bear Pelt", "Bear Claw", "Bear Meat"])
        hit_points = kwargs.get("hit_points", 60)

        entity.add_component(CreatureComponent(
            creature_name=creature_name,
            threat_level=threat_level,
            behavior=behavior,
            loot=loot,
            hit_points=hit_points
        ))

        # Add combat component
        attack = kwargs.get("attack", 25)
        defense = kwargs.get("defense", 10)
        health = kwargs.get("health", 60)
        stamina = kwargs.get("stamina", 40)

        combat_comp = CombatComponent(
            attack=attack,
            defense=defense,
            health=health,
            max_health=health,
            stamina=stamina,
            max_stamina=stamina
        )
        combat_comp.is_hostile = False  # Only becomes hostile when provoked
        combat_comp.critical_chance = 0.05  # 5% critical chance
        combat_comp.dodge_chance = 0.05    # 5% dodge chance
        entity.add_component(combat_comp)

        # Add AI component
        ai_comp = AIComponent(behavior_type="defensive")
        entity.add_component(ai_comp)

        # Add natural weapon component (claws/teeth)
        weapon_comp = WeaponComponent(
            weapon_type="natural",
            base_damage=15,
            damage_type="physical",
            attack_speed=0.8,  # Slower but stronger
            range=1.0,
            critical_bonus=0.1,
            special_effects={
                "maul": {
                    "chance": 0.15,
                    "duration": 3.0,
                    "modifiers": {
                        "defense": -5,  # Reduce defense
                        "dodge_chance": -0.05  # Reduce dodge chance
                    }
                }
            }
        )
        entity.add_component(weapon_comp)

        # Add natural armor component (thick hide)
        armor_comp = ArmorComponent(
            armor_type="natural",
            defense_bonus=8,
            resistances={
                "physical": 0.2,  # 20% physical resistance
                "cold": 0.3       # 30% cold resistance
            },
            weight=0.0  # Natural armor has no weight penalty
        )
        entity.add_component(armor_comp)

        return entity

    def _create_bandit(self, x: float, y: float, **kwargs) -> Entity:
        """
        Create a bandit NPC.

        Args:
            x: X position
            y: Y position
            **kwargs: Additional parameters

        Returns:
            The created bandit entity
        """
        # Create the entity
        entity = self.entity_manager.create_entity()

        # Add position component
        entity.add_component(PositionComponent(x, y))

        # Add creature component (using CreatureComponent for NPCs too)
        creature_name = kwargs.get("name", "Bandit")
        threat_level = kwargs.get("threat_level", "Medium")
        behavior = kwargs.get("behavior", "Aggressive")
        loot = kwargs.get("loot", ["Gold Coins", "Dagger", "Leather Scraps"])
        hit_points = kwargs.get("hit_points", 25)

        entity.add_component(CreatureComponent(
            creature_name=creature_name,
            threat_level=threat_level,
            behavior=behavior,
            loot=loot,
            hit_points=hit_points
        ))

        # Add combat component
        attack = kwargs.get("attack", 12)
        defense = kwargs.get("defense", 8)
        health = kwargs.get("health", 25)
        stamina = kwargs.get("stamina", 30)

        combat_comp = CombatComponent(
            attack=attack,
            defense=defense,
            health=health,
            max_health=health,
            stamina=stamina,
            max_stamina=stamina
        )
        combat_comp.is_hostile = True
        combat_comp.critical_chance = 0.1  # 10% critical chance
        combat_comp.dodge_chance = 0.2     # 20% dodge chance
        entity.add_component(combat_comp)

        # Add AI component
        ai_comp = AIComponent(behavior_type="aggressive")
        entity.add_component(ai_comp)

        # Add weapon component
        weapon_type = random.choice(["dagger", "shortsword", "club"])
        base_damage = 0
        attack_speed = 0.0

        if weapon_type == "dagger":
            base_damage = 6
            attack_speed = 1.5  # Fast
        elif weapon_type == "shortsword":
            base_damage = 8
            attack_speed = 1.2  # Medium
        else:  # club
            base_damage = 10
            attack_speed = 0.9  # Slow

        weapon_comp = WeaponComponent(
            weapon_type=weapon_type,
            base_damage=base_damage,
            damage_type="physical",
            attack_speed=attack_speed,
            range=1.0,
            critical_bonus=0.05,
            special_effects={}  # No special effects for basic weapons
        )
        entity.add_component(weapon_comp)

        # Add armor component
        armor_comp = ArmorComponent(
            armor_type="light",
            defense_bonus=5,
            resistances={
                "physical": 0.1  # 10% physical resistance
            },
            weight=1.0,
            slot="chest"
        )
        entity.add_component(armor_comp)

        return entity

    def _create_guard(self, x: float, y: float, **kwargs) -> Entity:
        """
        Create a guard NPC.

        Args:
            x: X position
            y: Y position
            **kwargs: Additional parameters

        Returns:
            The created guard entity
        """
        # Create the entity
        entity = self.entity_manager.create_entity()

        # Add position component
        entity.add_component(PositionComponent(x, y))

        # Add creature component
        creature_name = kwargs.get("name", "Guard")
        threat_level = kwargs.get("threat_level", "High")
        behavior = kwargs.get("behavior", "Patrol")
        loot = kwargs.get("loot", ["Gold Coins", "Guard Badge", "Iron Sword"])
        hit_points = kwargs.get("hit_points", 40)

        entity.add_component(CreatureComponent(
            creature_name=creature_name,
            threat_level=threat_level,
            behavior=behavior,
            loot=loot,
            hit_points=hit_points
        ))

        # Add combat component
        attack = kwargs.get("attack", 15)
        defense = kwargs.get("defense", 15)
        health = kwargs.get("health", 40)
        stamina = kwargs.get("stamina", 40)

        combat_comp = CombatComponent(
            attack=attack,
            defense=defense,
            health=health,
            max_health=health,
            stamina=stamina,
            max_stamina=stamina
        )
        combat_comp.is_hostile = False  # Only hostile to criminals/enemies
        combat_comp.critical_chance = 0.08  # 8% critical chance
        combat_comp.dodge_chance = 0.1     # 10% dodge chance
        entity.add_component(combat_comp)

        # Add AI component
        ai_comp = AIComponent(behavior_type="patrol")
        entity.add_component(ai_comp)

        # Add weapon component
        weapon_type = random.choice(["longsword", "mace", "spear"])
        base_damage = 0
        attack_speed = 0.0
        weapon_range = 1.0

        if weapon_type == "longsword":
            base_damage = 12
            attack_speed = 1.0  # Medium
        elif weapon_type == "mace":
            base_damage = 14
            attack_speed = 0.8  # Slow
        else:  # spear
            base_damage = 10
            attack_speed = 1.1  # Medium
            weapon_range = 2.0  # Longer range

        weapon_comp = WeaponComponent(
            weapon_type=weapon_type,
            base_damage=base_damage,
            damage_type="physical",
            attack_speed=attack_speed,
            range=weapon_range,
            critical_bonus=0.05,
            special_effects={}
        )
        entity.add_component(weapon_comp)

        # Add armor component
        armor_comp = ArmorComponent(
            armor_type="medium",
            defense_bonus=12,
            resistances={
                "physical": 0.2,  # 20% physical resistance
                "fire": 0.1       # 10% fire resistance
            },
            weight=2.0,
            slot="chest"
        )
        entity.add_component(armor_comp)

        return entity

    def _create_deer(self, x: float, y: float, **kwargs) -> Entity:
        """
        Create a deer creature (passive, flees from combat).

        Args:
            x: X position
            y: Y position
            **kwargs: Additional parameters

        Returns:
            The created deer entity
        """
        # Create the entity
        entity = self.entity_manager.create_entity()

        # Add position component
        entity.add_component(PositionComponent(x, y))

        # Add creature component
        creature_name = kwargs.get("name", "Deer")
        threat_level = kwargs.get("threat_level", "Low")
        behavior = kwargs.get("behavior", "Passive")
        loot = kwargs.get("loot", ["Deer Hide", "Venison"])
        hit_points = kwargs.get("hit_points", 15)

        entity.add_component(CreatureComponent(
            creature_name=creature_name,
            threat_level=threat_level,
            behavior=behavior,
            loot=loot,
            hit_points=hit_points
        ))

        # Add combat component (minimal combat ability)
        attack = kwargs.get("attack", 3)
        defense = kwargs.get("defense", 2)
        health = kwargs.get("health", 15)
        stamina = kwargs.get("stamina", 60)  # High stamina for running

        combat_comp = CombatComponent(
            attack=attack,
            defense=defense,
            health=health,
            max_health=health,
            stamina=stamina,
            max_stamina=stamina
        )
        combat_comp.is_hostile = False
        combat_comp.critical_chance = 0.01  # 1% critical chance
        combat_comp.dodge_chance = 0.3     # 30% dodge chance (good at avoiding)
        entity.add_component(combat_comp)

        # Add AI component
        ai_comp = AIComponent(behavior_type="passive")
        entity.add_component(ai_comp)

        # Add natural weapon component (antlers/hooves)
        weapon_comp = WeaponComponent(
            weapon_type="natural",
            base_damage=3,
            damage_type="physical",
            attack_speed=1.0,
            range=1.0,
            critical_bonus=0.0,
            special_effects={}
        )
        entity.add_component(weapon_comp)

        return entity

    def _create_rabbit(self, x: float, y: float, **kwargs) -> Entity:
        """
        Create a rabbit creature (very passive, flees quickly).

        Args:
            x: X position
            y: Y position
            **kwargs: Additional parameters

        Returns:
            The created rabbit entity
        """
        # Create the entity
        entity = self.entity_manager.create_entity()

        # Add position component
        entity.add_component(PositionComponent(x, y))

        # Add creature component
        creature_name = kwargs.get("name", "Rabbit")
        threat_level = kwargs.get("threat_level", "None")
        behavior = kwargs.get("behavior", "Passive")
        loot = kwargs.get("loot", ["Rabbit Fur", "Rabbit Meat"])
        hit_points = kwargs.get("hit_points", 5)

        entity.add_component(CreatureComponent(
            creature_name=creature_name,
            threat_level=threat_level,
            behavior=behavior,
            loot=loot,
            hit_points=hit_points
        ))

        # Add combat component (minimal combat ability)
        attack = kwargs.get("attack", 1)
        defense = kwargs.get("defense", 1)
        health = kwargs.get("health", 5)
        stamina = kwargs.get("stamina", 50)  # High stamina for running

        combat_comp = CombatComponent(
            attack=attack,
            defense=defense,
            health=health,
            max_health=health,
            stamina=stamina,
            max_stamina=stamina
        )
        combat_comp.is_hostile = False
        combat_comp.critical_chance = 0.0   # No critical chance
        combat_comp.dodge_chance = 0.4     # 40% dodge chance (very good at avoiding)
        entity.add_component(combat_comp)

        # Add AI component
        ai_comp = AIComponent(behavior_type="passive")
        entity.add_component(ai_comp)

        return entity

    def _create_generic_creature(self, creature_type: str, x: float, y: float, **kwargs) -> Entity:
        """
        Create a generic creature with basic components.

        Args:
            creature_type: Type of creature
            x: X position
            y: Y position
            **kwargs: Additional parameters

        Returns:
            The created creature entity
        """
        # Create the entity
        entity = self.entity_manager.create_entity()

        # Add position component
        entity.add_component(PositionComponent(x, y))

        # Add creature component
        creature_name = kwargs.get("name", creature_type.capitalize())
        threat_level = kwargs.get("threat_level", "Medium")
        behavior = kwargs.get("behavior", "Neutral")
        loot = kwargs.get("loot", [f"{creature_type.capitalize()} Remains"])
        hit_points = kwargs.get("hit_points", 20)

        entity.add_component(CreatureComponent(
            creature_name=creature_name,
            threat_level=threat_level,
            behavior=behavior,
            loot=loot,
            hit_points=hit_points
        ))

        # Add combat component
        attack = kwargs.get("attack", 10)
        defense = kwargs.get("defense", 5)
        health = kwargs.get("health", 20)
        stamina = kwargs.get("stamina", 30)

        combat_comp = CombatComponent(
            attack=attack,
            defense=defense,
            health=health,
            max_health=health,
            stamina=stamina,
            max_stamina=stamina
        )
        combat_comp.is_hostile = kwargs.get("is_hostile", False)
        combat_comp.critical_chance = 0.05  # 5% critical chance
        combat_comp.dodge_chance = 0.05     # 5% dodge chance
        entity.add_component(combat_comp)

        # Add AI component
        ai_behavior = kwargs.get("ai_behavior", "default")
        ai_comp = AIComponent(behavior_type=ai_behavior)
        entity.add_component(ai_comp)

        # Add weapon component if specified
        if "weapon_damage" in kwargs:
            weapon_comp = WeaponComponent(
                weapon_type="natural",
                base_damage=kwargs.get("weapon_damage", 5),
                damage_type=kwargs.get("damage_type", "physical"),
                attack_speed=kwargs.get("attack_speed", 1.0),
                range=kwargs.get("range", 1.0)
            )
            entity.add_component(weapon_comp)

        # Add armor component if specified
        if "armor_defense" in kwargs:
            armor_comp = ArmorComponent(
                armor_type="natural",
                defense_bonus=kwargs.get("armor_defense", 3),
                resistances=kwargs.get("resistances", {})
            )
            entity.add_component(armor_comp)

        return entity