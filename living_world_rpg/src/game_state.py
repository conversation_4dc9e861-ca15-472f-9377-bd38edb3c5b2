"""Game state management."""

import logging
from typing import List, Optional

from src.constants import COLOR_G<PERSON><PERSON>, COLOR_RED


class GameState:
    """Manages the game's state and provides feedback to the player."""
    
    def __init__(self):
        """Initialize the game state."""
        self.feedback_messages: List[str] = []
        self.success_color = COLOR_GREEN
        self.error_color = COLOR_RED
        self.current_screen: Optional[str] = None
        self.is_paused = False
        
    def show_feedback_message(self, message: str, is_success: bool = True) -> None:
        """Show a feedback message to the player.
        
        Args:
            message: The message to display.
            is_success: Whether this is a success message (True) or an error message (False).
        """
        color = self.success_color if is_success else self.error_color
        self.feedback_messages.append(f"{message}")
        logging.info(f"Feedback: {message}")
        
    def clear_feedback_messages(self) -> None:
        """Clear all feedback messages."""
        self.feedback_messages.clear()
