from abc import ABC, abstractmethod
from typing import Optional
import pygame

from ..core.game_state import GameState

class AbstractScreen(ABC):
    """Abstract base class for all game screens."""

    def __init__(self, gs: GameState) -> None:
        """Initialize the screen with the game state."""
        self.gs = gs
        self.font = pygame.font.Font(None, 30)
        self.small_font = pygame.font.Font(None, 24)

    @abstractmethod
    def handle_events(self) -> None:
        """Handle events for this screen."""
        pass

    @abstractmethod
    def update(self, dt: float) -> None:
        """Update the screen state."""
        pass

    @abstractmethod
    def render(self, screen: pygame.Surface) -> None:
        """Render the screen content."""
        pass

    def draw_button(self,
                   screen: pygame.Surface,
                   text: str,
                   x: int,
                   y: int,
                   width: Optional[int] = None,
                   height: Optional[int] = None,
                   font_size: int = 30,
                   color: tuple = (0, 150, 0),
                   hover_color: tuple = (0, 200, 0)) -> tuple[bool, pygame.Rect]:
        """Draw a button with hover effect."""
        if width is None:
            width = self.font.size(text)[0] + 20
        if height is None:
            height = font_size + 10

        button_rect = pygame.Rect(x, y, width, height)
        mouse_pos = pygame.mouse.get_pos()
        is_hovered = button_rect.collidepoint(mouse_pos)

        # Draw button background
        pygame.draw.rect(screen, hover_color if is_hovered else color, button_rect)
        pygame.draw.rect(screen, (0, 0, 0), button_rect, 2)

        # Draw text
        text_surface = self.font.render(text, True, (255, 255, 255))
        text_rect = text_surface.get_rect(center=button_rect.center)
        screen.blit(text_surface, text_rect)

        return is_hovered, button_rect

    def draw_text(self,
                 screen: pygame.Surface,
                 text: str,
                 x: int,
                 y: int,
                 color: tuple = (255, 255, 255)) -> None:
        """Draw text on the screen."""
        text_surface = self.font.render(text, True, color)
        screen.blit(text_surface, (x, y))
