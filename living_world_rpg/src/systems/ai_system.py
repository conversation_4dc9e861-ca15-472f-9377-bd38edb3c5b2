# File: src/systems/ai_system.py
"""
AI System for Living World RPG

Handles AI behavior for NPCs and creatures, including:
- Basic movement and decision making
- Combat behavior and targeting
- Daily schedules and activities
- Emotional responses to events
"""

import random
import math
import logging
from typing import Dict, List, Optional, Tuple, Any

from core.entity import Entity
from core.system_base import System
from core.components import (
    AIComponent, PositionComponent, CombatComponent,
    WeaponComponent, ArmorComponent, CreatureComponent
)


class AISystem(System):
    """
    System for handling AI behavior for NPCs and creatures.
    """
    def __init__(self):
        super().__init__()
        self.perception_range = 10.0  # How far AI entities can "see"

    def update(self, dt: float, entities: Dict[int, Entity]) -> None:
        """
        Update AI behavior for all entities with AI components.

        Args:
            dt: Time elapsed since last update
            entities: Dictionary of all entities
        """
        # Process each entity with an AI component
        for entity_id, entity in entities.items():
            ai_comp = entity.get_component(AIComponent)
            if not ai_comp:
                continue

            # Update based on behavior type
            if ai_comp.behavior_type == "aggressive":
                self._update_aggressive_behavior(dt, entity_id, entity, entities)
            elif ai_comp.behavior_type == "defensive":
                self._update_defensive_behavior(dt, entity_id, entity, entities)
            elif ai_comp.behavior_type == "passive":
                self._update_passive_behavior(dt, entity_id, entity, entities)
            elif ai_comp.behavior_type == "patrol":
                self._update_patrol_behavior(dt, entity_id, entity, entities)
            else:
                # Default behavior
                self._update_default_behavior(dt, entity_id, entity, entities)

    def _update_aggressive_behavior(self, dt: float, entity_id: int, entity: Entity, entities: Dict[int, Entity]) -> None:
        """
        Update behavior for aggressive entities that actively seek combat.

        Args:
            dt: Time elapsed since last update
            entity_id: ID of the entity
            entity: The entity
            entities: Dictionary of all entities
        """
        # Check if already in combat
        combat_comp = entity.get_component(CombatComponent)
        if combat_comp and combat_comp.in_combat:
            return  # Already in combat, handled by combat system

        # Look for targets within perception range
        target_id = self._find_nearest_target(entity_id, entity, entities)

        if target_id is not None:
            # Found a target, engage in combat
            if combat_comp:
                combat_comp.is_hostile = True
                combat_comp.target_id = target_id

                # Notify combat system to start combat
                if hasattr(self, "game_state") and hasattr(self.game_state, "combat_system"):
                    self.game_state.combat_system.start_combat(entity_id, target_id, entities)

            # Move toward target if not in range
            self._move_toward_target(dt, entity, entities.get(target_id))
        else:
            # No target found, wander randomly
            self._wander_randomly(dt, entity)

    def _update_defensive_behavior(self, dt: float, entity_id: int, entity: Entity, entities: Dict[int, Entity]) -> None:
        """
        Update behavior for defensive entities that only fight when attacked.

        Args:
            dt: Time elapsed since last update
            entity_id: ID of the entity
            entity: The entity
            entities: Dictionary of all entities
        """
        # Check if already in combat
        combat_comp = entity.get_component(CombatComponent)
        if combat_comp and combat_comp.in_combat:
            return  # Already in combat, handled by combat system

        # If not in combat, just wander or stand guard
        self._wander_randomly(dt, entity, range_factor=0.5)  # Wander in a smaller area

    def _update_passive_behavior(self, dt: float, entity_id: int, entity: Entity, entities: Dict[int, Entity]) -> None:
        """
        Update behavior for passive entities that flee from combat.

        Args:
            dt: Time elapsed since last update
            entity_id: ID of the entity
            entity: The entity
            entities: Dictionary of all entities
        """
        # Check if in combat
        combat_comp = entity.get_component(CombatComponent)
        if combat_comp and combat_comp.in_combat:
            # Try to flee from combat
            if combat_comp.target_id and combat_comp.target_id in entities:
                target = entities[combat_comp.target_id]
                self._flee_from_target(dt, entity, target)

                # Attempt to end combat
                if hasattr(self, "game_state") and hasattr(self.game_state, "combat_system"):
                    self.game_state.combat_system.stop_combat(entity_id, entities)
        else:
            # Not in combat, wander peacefully
            self._wander_randomly(dt, entity)

    def _update_patrol_behavior(self, dt: float, entity_id: int, entity: Entity, entities: Dict[int, Entity]) -> None:
        """
        Update behavior for entities that patrol an area and engage hostiles.

        Args:
            dt: Time elapsed since last update
            entity_id: ID of the entity
            entity: The entity
            entities: Dictionary of all entities
        """
        ai_comp = entity.get_component(AIComponent)

        # Check if already in combat
        combat_comp = entity.get_component(CombatComponent)
        if combat_comp and combat_comp.in_combat:
            return  # Already in combat, handled by combat system

        # Check for hostile entities in perception range
        target_id = self._find_nearest_hostile(entity_id, entity, entities)

        if target_id is not None:
            # Found a hostile, engage in combat
            if combat_comp:
                combat_comp.is_hostile = True
                combat_comp.target_id = target_id

                # Notify combat system to start combat
                if hasattr(self, "game_state") and hasattr(self.game_state, "combat_system"):
                    self.game_state.combat_system.start_combat(entity_id, target_id, entities)
        else:
            # No hostiles, continue patrol
            self._follow_patrol_path(dt, entity, ai_comp)

    def _update_default_behavior(self, dt: float, entity_id: int, entity: Entity, entities: Dict[int, Entity]) -> None:
        """
        Update default behavior for entities with no specific behavior type.

        Args:
            dt: Time elapsed since last update
            entity_id: ID of the entity
            entity: The entity
            entities: Dictionary of all entities
        """
        # Simple wandering behavior
        self._wander_randomly(dt, entity)

    def _find_nearest_target(self, entity_id: int, entity: Entity, entities: Dict[int, Entity]) -> Optional[int]:
        """
        Find the nearest valid target for combat.

        Args:
            entity_id: ID of the entity
            entity: The entity
            entities: Dictionary of all entities

        Returns:
            ID of the nearest target, or None if no valid target found
        """
        pos_comp = entity.get_component(PositionComponent)
        if not pos_comp:
            return None

        nearest_target = None
        nearest_distance = float('inf')

        for target_id, target in entities.items():
            # Skip self
            if target_id == entity_id:
                continue

            # Skip entities without position or combat components
            target_pos = target.get_component(PositionComponent)
            target_combat = target.get_component(CombatComponent)
            if not target_pos or not target_combat:
                continue

            # Skip dead entities
            if not target_combat.is_alive():
                continue

            # Calculate distance
            distance = math.sqrt((pos_comp.x - target_pos.x) ** 2 + (pos_comp.y - target_pos.y) ** 2)

            # Check if within perception range and closer than current nearest
            if distance <= self.perception_range and distance < nearest_distance:
                # Check if target is a valid target (e.g., player or different faction)
                if self._is_valid_target(entity, target):
                    nearest_target = target_id
                    nearest_distance = distance

        return nearest_target

    def _find_nearest_hostile(self, entity_id: int, entity: Entity, entities: Dict[int, Entity]) -> Optional[int]:
        """
        Find the nearest hostile entity.

        Args:
            entity_id: ID of the entity
            entity: The entity
            entities: Dictionary of all entities

        Returns:
            ID of the nearest hostile, or None if no hostile found
        """
        pos_comp = entity.get_component(PositionComponent)
        if not pos_comp:
            return None

        nearest_hostile = None
        nearest_distance = float('inf')

        for target_id, target in entities.items():
            # Skip self
            if target_id == entity_id:
                continue

            # Skip entities without position or combat components
            target_pos = target.get_component(PositionComponent)
            target_combat = target.get_component(CombatComponent)
            if not target_pos or not target_combat:
                continue

            # Skip dead entities
            if not target_combat.is_alive():
                continue

            # Skip non-hostile entities
            if not target_combat.is_hostile:
                continue

            # Calculate distance
            distance = math.sqrt((pos_comp.x - target_pos.x) ** 2 + (pos_comp.y - target_pos.y) ** 2)

            # Check if within perception range and closer than current nearest
            if distance <= self.perception_range and distance < nearest_distance:
                nearest_hostile = target_id
                nearest_distance = distance

        return nearest_hostile

    def _is_valid_target(self, entity: Entity, target: Entity) -> bool:
        """
        Check if a target is valid for combat.

        Args:
            entity: The entity
            target: The potential target

        Returns:
            True if target is valid, False otherwise
        """
        # Check if target has player component (always a valid target for aggressive NPCs)
        if target.get_component("PlayerComponent"):
            return True

        # Check faction relationships (if applicable)
        entity_faction = entity.get_component("FactionComponent")
        target_faction = target.get_component("FactionComponent")

        if entity_faction and target_faction:
            # Entities of the same faction don't attack each other
            if entity_faction.faction_name == target_faction.faction_name:
                return False

            # TODO: Check faction relationships from a faction relationship manager

        return True  # Default to valid target

    def _move_toward_target(self, dt: float, entity: Entity, target: Optional[Entity]) -> None:
        """
        Move entity toward a target.

        Args:
            dt: Time elapsed since last update
            entity: The entity
            target: The target entity
        """
        if not target:
            return

        pos_comp = entity.get_component(PositionComponent)
        target_pos = target.get_component(PositionComponent)

        if not pos_comp or not target_pos:
            return

        # Calculate direction vector
        dx = target_pos.x - pos_comp.x
        dy = target_pos.y - pos_comp.y

        # Normalize direction
        distance = math.sqrt(dx * dx + dy * dy)
        if distance < 0.1:
            return  # Already at target

        dx /= distance
        dy /= distance

        # Move toward target (speed could be entity-specific)
        speed = 2.0 * dt
        pos_comp.x += dx * speed
        pos_comp.y += dy * speed

    def _flee_from_target(self, dt: float, entity: Entity, target: Entity) -> None:
        """
        Move entity away from a target.

        Args:
            dt: Time elapsed since last update
            entity: The entity
            target: The target to flee from
        """
        pos_comp = entity.get_component(PositionComponent)
        target_pos = target.get_component(PositionComponent)

        if not pos_comp or not target_pos:
            return

        # Calculate direction vector (away from target)
        dx = pos_comp.x - target_pos.x
        dy = pos_comp.y - target_pos.y

        # Normalize direction
        distance = math.sqrt(dx * dx + dy * dy)
        if distance < 0.1:
            # If very close, move in a random direction
            dx = random.uniform(-1.0, 1.0)
            dy = random.uniform(-1.0, 1.0)
            distance = math.sqrt(dx * dx + dy * dy)

        dx /= distance
        dy /= distance

        # Move away from target (speed could be entity-specific)
        speed = 3.0 * dt  # Fleeing is faster than normal movement
        pos_comp.x += dx * speed
        pos_comp.y += dy * speed

    def _wander_randomly(self, dt: float, entity: Entity, range_factor: float = 1.0) -> None:
        """
        Make entity wander randomly.

        Args:
            dt: Time elapsed since last update
            entity: The entity
            range_factor: Factor to adjust wandering range (1.0 is normal)
        """
        ai_comp = entity.get_component(AIComponent)
        pos_comp = entity.get_component(PositionComponent)

        if not ai_comp or not pos_comp:
            return

        # Check if we need a new wander target
        if "wander_target_x" not in ai_comp.blackboard or "wander_target_y" not in ai_comp.blackboard:
            # Set a new random target within range
            wander_range = 5.0 * range_factor
            ai_comp.blackboard["wander_target_x"] = pos_comp.x + random.uniform(-wander_range, wander_range)
            ai_comp.blackboard["wander_target_y"] = pos_comp.y + random.uniform(-wander_range, wander_range)
            ai_comp.blackboard["wander_time"] = random.uniform(3.0, 8.0)  # Time to spend at target

        # Check if we've reached the target
        target_x = ai_comp.blackboard["wander_target_x"]
        target_y = ai_comp.blackboard["wander_target_y"]

        dx = target_x - pos_comp.x
        dy = target_y - pos_comp.y
        distance = math.sqrt(dx * dx + dy * dy)

        if distance < 0.5:
            # At target, wait for a bit
            ai_comp.blackboard["wander_time"] -= dt
            if ai_comp.blackboard["wander_time"] <= 0:
                # Time's up, clear target to get a new one next update
                del ai_comp.blackboard["wander_target_x"]
                del ai_comp.blackboard["wander_target_y"]
                del ai_comp.blackboard["wander_time"]
        else:
            # Move toward target
            dx /= distance
            dy /= distance

            speed = 1.0 * dt
            pos_comp.x += dx * speed
            pos_comp.y += dy * speed

    def _follow_patrol_path(self, dt: float, entity: Entity, ai_comp: AIComponent) -> None:
        """
        Make entity follow a patrol path.

        Args:
            dt: Time elapsed since last update
            entity: The entity
            ai_comp: The entity's AI component
        """
        pos_comp = entity.get_component(PositionComponent)

        if not pos_comp:
            return

        # Check if we have patrol points
        if "patrol_points" not in ai_comp.blackboard:
            # Create a simple patrol path around current position
            center_x = pos_comp.x
            center_y = pos_comp.y
            radius = 5.0
            num_points = 4

            patrol_points = []
            for i in range(num_points):
                angle = 2 * math.pi * i / num_points
                x = center_x + radius * math.cos(angle)
                y = center_y + radius * math.sin(angle)
                patrol_points.append((x, y))

            ai_comp.blackboard["patrol_points"] = patrol_points
            ai_comp.blackboard["current_patrol_index"] = 0

        # Get current patrol point
        patrol_points = ai_comp.blackboard["patrol_points"]
        current_index = ai_comp.blackboard["current_patrol_index"]
        target_x, target_y = patrol_points[current_index]

        # Move toward current patrol point
        dx = target_x - pos_comp.x
        dy = target_y - pos_comp.y
        distance = math.sqrt(dx * dx + dy * dy)

        if distance < 0.5:
            # Reached point, move to next one
            ai_comp.blackboard["current_patrol_index"] = (current_index + 1) % len(patrol_points)
        else:
            # Move toward point
            dx /= distance
            dy /= distance

            speed = 1.5 * dt
            pos_comp.x += dx * speed
            pos_comp.y += dy * speed
