# File: src/systems/combat_system.py
"""
Combat System for Living World RPG

Handles combat interactions between entities, including:
- Attack and defense calculations
- Damage application
- Critical hits and dodges
- Status effect application
- Combat state management
"""

import random
import math
import logging
from typing import Dict, List, Optional, Tuple, Any

from core.entity import Entity
from core.system_base import System
from core.components import CombatComponent, WeaponComponent, ArmorComponent, PositionComponent


class CombatSystem(System):
    """
    System for handling combat between entities.
    """
    def __init__(self):
        super().__init__()
        self.active_combats = {}  # Maps entity_id to target_id
        self.combat_log = []  # Stores recent combat events
        self.max_log_entries = 100

    def update(self, dt: float, entities: Dict[int, Entity]) -> None:
        """
        Update combat state for all entities.

        Args:
            dt: Time elapsed since last update
            entities: Dictionary of all entities
        """
        # Update status effects for all entities with combat components
        for entity_id, entity in entities.items():
            combat_comp = entity.get_component(CombatComponent)
            if combat_comp:
                combat_comp.update_status_effects(dt)

                # Check if entity is still alive
                if not combat_comp.is_alive():
                    self._handle_entity_death(entity_id, entity, entities)

        # Process active combats
        self._process_active_combats(dt, entities)

    def _process_active_combats(self, dt: float, entities: Dict[int, Entity]) -> None:
        """
        Process all active combat engagements.

        Args:
            dt: Time elapsed since last update
            entities: Dictionary of all entities
        """
        combats_to_remove = []

        for attacker_id, target_id in self.active_combats.items():
            # Check if both entities still exist and are alive
            if (attacker_id not in entities or target_id not in entities or
                not self._is_entity_alive(entities, attacker_id) or
                not self._is_entity_alive(entities, target_id)):
                combats_to_remove.append(attacker_id)
                continue

            attacker = entities[attacker_id]
            target = entities[target_id]

            # Check if entities are in range
            if not self._are_entities_in_range(attacker, target):
                combats_to_remove.append(attacker_id)
                continue

            # Process attack if enough time has passed since last attack
            attacker_combat = attacker.get_component(CombatComponent)
            current_time = self.game_state.game_time if hasattr(self, 'game_state') else 0

            if current_time - attacker_combat.last_attack_time >= self._get_attack_cooldown(attacker):
                self._process_attack(attacker_id, attacker, target_id, target)
                attacker_combat.last_attack_time = current_time

        # Remove finished combats
        for attacker_id in combats_to_remove:
            if attacker_id in self.active_combats:
                del self.active_combats[attacker_id]

    def start_combat(self, attacker_id: int, target_id: int, entities: Dict[int, Entity]) -> bool:
        """
        Start combat between two entities.

        Args:
            attacker_id: ID of the attacking entity
            target_id: ID of the target entity
            entities: Dictionary of all entities

        Returns:
            True if combat was successfully started, False otherwise
        """
        # Validate entities
        if attacker_id not in entities or target_id not in entities:
            return False

        attacker = entities[attacker_id]
        target = entities[target_id]

        # Check if both entities have combat components
        attacker_combat = attacker.get_component(CombatComponent)
        target_combat = target.get_component(CombatComponent)

        if not attacker_combat or not target_combat:
            return False

        # Check if entities are in range
        if not self._are_entities_in_range(attacker, target):
            return False

        # Set combat state
        attacker_combat.in_combat = True
        attacker_combat.target_id = target_id
        target_combat.in_combat = True

        # Add to active combats
        self.active_combats[attacker_id] = target_id

        # Log combat start
        self._add_to_combat_log(f"{self._get_entity_name(attacker)} attacks {self._get_entity_name(target)}!")

        return True

    def stop_combat(self, entity_id: int, entities: Dict[int, Entity]) -> None:
        """
        Stop combat for an entity.

        Args:
            entity_id: ID of the entity
            entities: Dictionary of all entities
        """
        if entity_id in self.active_combats:
            target_id = self.active_combats[entity_id]
            del self.active_combats[entity_id]

            # Update combat state
            if entity_id in entities:
                entity = entities[entity_id]
                combat_comp = entity.get_component(CombatComponent)
                if combat_comp:
                    combat_comp.in_combat = False
                    combat_comp.target_id = None

            # Log combat end
            if entity_id in entities and target_id in entities:
                self._add_to_combat_log(
                    f"{self._get_entity_name(entities[entity_id])} disengages from "
                    f"{self._get_entity_name(entities[target_id])}."
                )

    def _process_attack(self, attacker_id: int, attacker: Entity, target_id: int, target: Entity) -> None:
        """
        Process an attack between two entities.

        Args:
            attacker_id: ID of the attacking entity
            attacker: The attacking entity
            target_id: ID of the target entity
            target: The target entity
        """
        attacker_combat = attacker.get_component(CombatComponent)
        target_combat = target.get_component(CombatComponent)

        # Get weapon and armor components
        attacker_weapon = attacker.get_component(WeaponComponent)
        target_armor = target.get_component(ArmorComponent)

        # Calculate base damage
        base_damage = self._calculate_base_damage(attacker, attacker_combat, attacker_weapon)

        # Check if attack hits
        hit_result = self._calculate_hit_result(attacker, attacker_combat, target, target_combat)

        if hit_result == "miss":
            self._add_to_combat_log(f"{self._get_entity_name(attacker)} misses {self._get_entity_name(target)}!")
            return

        # Calculate final damage
        damage_type = "physical"
        if attacker_weapon:
            damage_type = attacker_weapon.damage_type

        final_damage = self._calculate_final_damage(
            base_damage,
            hit_result == "critical",
            attacker_combat,
            target_combat,
            target_armor,
            damage_type
        )

        # Apply damage
        actual_damage = target_combat.take_damage(final_damage, damage_type)

        # Log the result
        if hit_result == "critical":
            self._add_to_combat_log(
                f"{self._get_entity_name(attacker)} lands a critical hit on "
                f"{self._get_entity_name(target)} for {actual_damage} damage!"
            )
        else:
            self._add_to_combat_log(
                f"{self._get_entity_name(attacker)} hits {self._get_entity_name(target)} "
                f"for {actual_damage} damage!"
            )

        # Apply weapon durability loss
        if attacker_weapon:
            if attacker_weapon.use_weapon() is False:
                self._add_to_combat_log(f"{self._get_entity_name(attacker)}'s weapon broke!")

        # Apply armor durability loss
        if target_armor:
            if target_armor.take_damage(final_damage) is False:
                self._add_to_combat_log(f"{self._get_entity_name(target)}'s armor broke!")

        # Check for special effects
        self._apply_special_effects(attacker, target, hit_result)

    def _calculate_base_damage(self, attacker: Entity,
                              attacker_combat: CombatComponent,
                              attacker_weapon: Optional[WeaponComponent]) -> int:
        """
        Calculate the base damage for an attack.

        Args:
            attacker: The attacking entity
            attacker_combat: The attacker's combat component
            attacker_weapon: The attacker's weapon component (if any)

        Returns:
            The base damage value
        """
        # Start with the attacker's effective attack value
        base_damage = attacker_combat.get_effective_attack()

        # Add weapon damage if available
        if attacker_weapon:
            base_damage += attacker_weapon.get_effective_damage()

        return max(1, base_damage)  # Minimum damage is 1

    def _calculate_hit_result(self, attacker: Entity, attacker_combat: CombatComponent,
                             target: Entity, target_combat: CombatComponent) -> str:
        """
        Calculate if an attack hits, misses, or is a critical hit.

        Args:
            attacker: The attacking entity
            attacker_combat: The attacker's combat component
            target: The target entity
            target_combat: The target's combat component

        Returns:
            "hit", "miss", or "critical"
        """
        # Get weapon for critical bonus
        attacker_weapon = attacker.get_component(WeaponComponent)
        critical_chance = attacker_combat.critical_chance
        if attacker_weapon:
            critical_chance += attacker_weapon.critical_bonus

        # Check for critical hit
        if random.random() < critical_chance:
            return "critical"

        # Check for dodge
        if random.random() < target_combat.dodge_chance:
            return "miss"

        # Normal hit
        return "hit"

    def _calculate_final_damage(self, base_damage: int, is_critical: bool,
                               attacker_combat: CombatComponent, target_combat: CombatComponent,
                               target_armor: Optional[ArmorComponent], damage_type: str) -> int:
        """
        Calculate the final damage after applying modifiers.

        Args:
            base_damage: The base damage value
            is_critical: Whether this is a critical hit
            attacker_combat: The attacker's combat component
            target_combat: The target's combat component
            target_armor: The target's armor component (if any)
            damage_type: The type of damage being dealt

        Returns:
            The final damage value
        """
        # Apply critical multiplier
        damage = base_damage * (2.0 if is_critical else 1.0)

        # Apply defense reduction
        defense = target_combat.get_effective_defense()
        if target_armor:
            defense += target_armor.get_effective_defense()

        # Defense reduces damage by a percentage (capped at 75%)
        defense_factor = min(0.75, defense / 100.0)
        damage = int(damage * (1.0 - defense_factor))

        # Apply resistances
        resistance = target_combat.resistances.get(damage_type, 0.0)
        if target_armor:
            resistance += target_armor.get_resistance(damage_type)

        # Cap resistance at 90%
        resistance = min(0.9, resistance)
        damage = int(damage * (1.0 - resistance))

        return max(1, damage)  # Minimum damage is 1

    def _apply_special_effects(self, attacker: Entity, target: Entity, hit_result: str) -> None:
        """
        Apply special effects from weapons or abilities.

        Args:
            attacker: The attacking entity
            target: The target entity
            hit_result: The result of the hit ("hit", "miss", or "critical")
        """
        # Only apply effects on hit or critical
        if hit_result == "miss":
            return

        attacker_weapon = attacker.get_component(WeaponComponent)
        target_combat = target.get_component(CombatComponent)

        if not attacker_weapon or not target_combat:
            return

        # Apply weapon special effects
        for effect_name, effect_data in attacker_weapon.special_effects.items():
            # Check if effect should trigger
            if "chance" in effect_data and random.random() > effect_data["chance"]:
                continue

            # Critical hits have a higher chance to apply effects
            if hit_result == "critical" and random.random() < 0.5:
                pass  # Proceed with effect application

            # Apply the effect
            if "duration" in effect_data and "modifiers" in effect_data:
                target_combat.add_status_effect(
                    effect_name,
                    effect_data["duration"],
                    effect_data["modifiers"]
                )

                self._add_to_combat_log(
                    f"{self._get_entity_name(target)} is affected by {effect_name}!"
                )

    def _handle_entity_death(self, entity_id: int, entity: Entity, entities: Dict[int, Entity]) -> None:
        """
        Handle an entity's death.

        Args:
            entity_id: ID of the dead entity
            entity: The dead entity
            entities: Dictionary of all entities
        """
        # Log death
        self._add_to_combat_log(f"{self._get_entity_name(entity)} has died!")

        # Stop any active combats involving this entity
        self.stop_combat(entity_id, entities)

        # Remove entity from any active combats where it's the target
        combats_to_stop = []
        for attacker_id, target_id in self.active_combats.items():
            if target_id == entity_id:
                combats_to_stop.append(attacker_id)

        for attacker_id in combats_to_stop:
            self.stop_combat(attacker_id, entities)

        # TODO: Handle loot drops, XP rewards, etc.

    def _are_entities_in_range(self, entity1: Entity, entity2: Entity) -> bool:
        """
        Check if two entities are within combat range of each other.

        Args:
            entity1: First entity
            entity2: Second entity

        Returns:
            True if entities are in range, False otherwise
        """
        pos1 = entity1.get_component(PositionComponent)
        pos2 = entity2.get_component(PositionComponent)

        if not pos1 or not pos2:
            return False

        # Get weapon range for entity1
        weapon = entity1.get_component(WeaponComponent)
        range_value = 1.0  # Default melee range
        if weapon:
            range_value = weapon.range

        # Calculate distance
        distance = math.sqrt((pos1.x - pos2.x) ** 2 + (pos1.y - pos2.y) ** 2)

        return distance <= range_value

    def _get_attack_cooldown(self, entity: Entity) -> float:
        """
        Get the cooldown time between attacks for an entity.

        Args:
            entity: The entity

        Returns:
            Cooldown time in seconds
        """
        combat_comp = entity.get_component(CombatComponent)
        weapon_comp = entity.get_component(WeaponComponent)

        base_cooldown = 1.0  # Default 1 second between attacks

        if combat_comp:
            base_cooldown /= combat_comp.attack_speed

        if weapon_comp:
            base_cooldown /= weapon_comp.attack_speed

        return max(0.1, base_cooldown)  # Minimum cooldown of 0.1 seconds

    def _is_entity_alive(self, entities: Dict[int, Entity], entity_id: int) -> bool:
        """
        Check if an entity is alive.

        Args:
            entities: Dictionary of all entities
            entity_id: ID of the entity to check

        Returns:
            True if entity is alive, False otherwise
        """
        if entity_id not in entities:
            return False

        entity = entities[entity_id]
        combat_comp = entity.get_component(CombatComponent)

        return combat_comp and combat_comp.is_alive()

    def _get_entity_name(self, entity: Entity) -> str:
        """
        Get a display name for an entity.

        Args:
            entity: The entity

        Returns:
            Display name for the entity
        """
        # Try different components that might have a name
        player_comp = entity.get_component("PlayerComponent")
        if player_comp and hasattr(player_comp, "name"):
            return player_comp.name

        creature_comp = entity.get_component("CreatureComponent")
        if creature_comp and hasattr(creature_comp, "creature_name"):
            return creature_comp.creature_name

        # Fallback to entity ID
        return f"Entity {entity.id}"

    def _add_to_combat_log(self, message: str) -> None:
        """
        Add a message to the combat log.

        Args:
            message: The message to add
        """
        self.combat_log.append(message)

        # Trim log if it gets too long
        if len(self.combat_log) > self.max_log_entries:
            self.combat_log = self.combat_log[-self.max_log_entries:]

        # Also log to console for debugging
        logging.debug(f"[Combat] {message}")

    def get_combat_log(self, max_entries: int = 10) -> List[str]:
        """
        Get recent entries from the combat log.

        Args:
            max_entries: Maximum number of entries to return

        Returns:
            List of recent combat log entries
        """
        return self.combat_log[-max_entries:]