"""
Test script for the AI systems in Living World RPG.

This script tests the behavior tree, GOAP, scheduling, and social systems.
"""

import sys
import os
import logging
import random
import time

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Add the src directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import game modules
from ..core.ecs import ECSWorld
from ..core.components import AIComponent, PositionComponent, CreatureComponent, InventoryComponent, StatsComponent
from ..ai.behavior_tree import BehaviorTree, Selector, Sequence, Action, Condition, Blackboard, NodeStatus
from ..ai.goap import GoapAgent, WorldState, Goal, Action as GoapAction
from ..ai.scheduling import ScheduleManager, TimeOfDay, Schedule, Activity, ActivityPriority, WeatherType, EventType
from ..ai.social import EmotionalState, SocialNetwork

# Create a test world
world = ECSWorld()

def create_test_npc(name, x, y, behavior_type="basic"):
    """Create a test NPC with AI capabilities."""
    npc = world.create_entity()
    npc.add_component(PositionComponent(x, y))
    npc.add_component(CreatureComponent(name, "humanoid", 20, 20))
    npc.add_component(InventoryComponent())
    npc.add_component(StatsComponent())

    # Add AI component
    ai_comp = AIComponent(behavior_type)
    npc.add_component(ai_comp)

    # Set up personality traits based on random values
    for trait in ai_comp.personality:
        ai_comp.set_personality_trait(trait, random.random())

    # Set up initial schedule based on behavior type
    if behavior_type == "behavior_tree":
        # Create a simple behavior tree for the NPC
        blackboard = Blackboard()
        blackboard.set("entity", npc)

        # Define conditions and actions
        is_hungry = Condition(lambda bb: bb.get("hunger", 0) > 70, "IsHungry")
        find_food = Action(lambda bb: logging.info(f"[AI] {name} is looking for food") or True and NodeStatus.SUCCESS, "FindFood")
        eat_food = Action(lambda bb: logging.info(f"[AI] {name} is eating") or True and NodeStatus.SUCCESS, "EatFood")

        is_tired = Condition(lambda bb: bb.get("energy", 0) < 30, "IsTired")
        find_bed = Action(lambda bb: logging.info(f"[AI] {name} is looking for a bed") or True and NodeStatus.SUCCESS, "FindBed")
        sleep = Action(lambda bb: logging.info(f"[AI] {name} is sleeping") or True and NodeStatus.SUCCESS, "Sleep")

        wander = Action(lambda bb: logging.info(f"[AI] {name} is wandering") or True and NodeStatus.SUCCESS, "Wander")

        # Build the tree
        hungry_sequence = Sequence("HungrySequence")
        hungry_sequence.add_child(is_hungry)
        hungry_sequence.add_child(find_food)
        hungry_sequence.add_child(eat_food)

        tired_sequence = Sequence("TiredSequence")
        tired_sequence.add_child(is_tired)
        tired_sequence.add_child(find_bed)
        tired_sequence.add_child(sleep)

        root = Selector("RootSelector")
        root.add_child(hungry_sequence)
        root.add_child(tired_sequence)
        root.add_child(wander)

        # Create the behavior tree and store it in the AI component
        behavior_tree = BehaviorTree(root)
        behavior_tree.blackboard = blackboard
        ai_comp.behavior_tree = behavior_tree

    elif behavior_type == "goap":
        # Create a GOAP agent for the NPC
        agent = GoapAgent(name)

        # Define actions
        gather_action = GoapAction("GatherResource", cost=1)
        gather_action.add_precondition("has_resource", False)
        gather_action.add_effect("has_resource", True)

        store_action = GoapAction("StoreResource", cost=1)
        store_action.add_precondition("has_resource", True)
        store_action.add_precondition("at_storage", True)
        store_action.add_effect("has_resource", False)
        store_action.add_effect("resources_stored", True)

        move_to_storage = GoapAction("MoveToStorage", cost=2)
        move_to_storage.add_precondition("at_storage", False)
        move_to_storage.add_effect("at_storage", True)

        # Add actions to the agent
        agent.add_action(gather_action)
        agent.add_action(store_action)
        agent.add_action(move_to_storage)

        # Define a goal
        goal = Goal("StoreResources")
        goal.add_condition("resources_stored", True)
        agent.add_goal(goal)

        # Set current goal
        agent.set_current_goal("StoreResources")

        # Set initial world state
        agent.world_state.set("has_resource", False)
        agent.world_state.set("at_storage", False)
        agent.world_state.set("resources_stored", False)

        # Store the agent in the AI component
        ai_comp.goap_agent = agent

    logging.info(f"[Test] Created NPC '{name}' with {behavior_type} AI")
    return npc

def test_behavior_tree():
    """Test the behavior tree system."""
    logging.info("=== Testing Behavior Tree System ===")

    # Create an NPC with a behavior tree
    npc = create_test_npc("Villager Bob", 10, 10, "behavior_tree")
    ai_comp = npc.get_component(AIComponent)

    # Test different scenarios
    logging.info("--- Testing hungry scenario ---")
    ai_comp.behavior_tree.blackboard.set("hunger", 80)
    ai_comp.behavior_tree.blackboard.set("energy", 50)
    status = ai_comp.behavior_tree.tick()
    logging.info(f"Behavior tree status: {status}")

    logging.info("--- Testing tired scenario ---")
    ai_comp.behavior_tree.blackboard.set("hunger", 50)
    ai_comp.behavior_tree.blackboard.set("energy", 20)
    status = ai_comp.behavior_tree.tick()
    logging.info(f"Behavior tree status: {status}")

    logging.info("--- Testing default scenario ---")
    ai_comp.behavior_tree.blackboard.set("hunger", 50)
    ai_comp.behavior_tree.blackboard.set("energy", 50)
    status = ai_comp.behavior_tree.tick()
    logging.info(f"Behavior tree status: {status}")

def test_goap():
    """Test the GOAP system."""
    logging.info("=== Testing GOAP System ===")

    # Create an NPC with GOAP
    npc = create_test_npc("Merchant Alice", 15, 15, "goap")
    ai_comp = npc.get_component(AIComponent)

    # Test different scenarios
    logging.info("--- Testing initial state ---")
    action = ai_comp.goap_agent.think()
    if action:
        logging.info(f"Selected action: {action.name}")
    else:
        logging.info("No action selected")

    logging.info("--- Testing with resource ---")
    ai_comp.goap_agent.world_state.set("has_resource", True)
    action = ai_comp.goap_agent.think()
    if action:
        logging.info(f"Selected action: {action.name}")
    else:
        logging.info("No action selected")

    logging.info("--- Testing at storage with resource ---")
    ai_comp.goap_agent.world_state.set("has_resource", True)
    ai_comp.goap_agent.world_state.set("at_storage", True)
    action = ai_comp.goap_agent.think()
    if action:
        logging.info(f"Selected action: {action.name}")
    else:
        logging.info("No action selected")

def test_scheduling():
    """Test the scheduling system."""
    logging.info("=== Testing Scheduling System ===")

    # Create a schedule manager
    schedule_manager = ScheduleManager()

    # Create a schedule for a villager
    villager_id = "villager1"
    villager_schedule = Schedule(villager_id)

    # Define some locations
    home_location = (10, 10)
    field_location = (20, 20)
    market_location = (30, 30)

    # Create activities
    work_field = Activity("Work in Field", field_location, ActivityPriority.HIGH)
    eat_lunch = Activity("Eat Lunch", home_location, ActivityPriority.MEDIUM)
    socialize = Activity("Socialize", market_location, ActivityPriority.LOW)
    sleep = Activity("Sleep", home_location, ActivityPriority.HIGH)

    # Add entries to the schedule
    villager_schedule.add_entry(TimeOfDay.MORNING, work_field)
    villager_schedule.add_entry(TimeOfDay.NOON, eat_lunch)
    villager_schedule.add_entry(TimeOfDay.AFTERNOON, work_field)
    villager_schedule.add_entry(TimeOfDay.EVENING, socialize)
    villager_schedule.add_entry(TimeOfDay.NIGHT, sleep)

    # Add the schedule to the manager
    schedule_manager.add_schedule(villager_id, villager_schedule)

    # Test different times of day
    for time_of_day in TimeOfDay:
        schedule_manager.current_time_of_day = time_of_day
        activity = schedule_manager.get_current_activity(villager_id)
        if activity:
            logging.info(f"Villager activity at {time_of_day.name}: {activity.name}")
        else:
            logging.info(f"Villager has no activity at {time_of_day.name}")

def test_social():
    """Test the social system."""
    logging.info("=== Testing Social System ===")

    # We'll use the AIComponent's built-in emotional and social capabilities

    # Create some NPCs
    bob = create_test_npc("Bob", 10, 10)
    alice = create_test_npc("Alice", 15, 15)
    charlie = create_test_npc("Charlie", 20, 20)

    # Set up relationships
    bob_ai = bob.get_component(AIComponent)
    alice_ai = alice.get_component(AIComponent)
    charlie_ai = charlie.get_component(AIComponent)

    bob_ai.set_relationship("Alice", 0.8)  # Bob likes Alice
    alice_ai.set_relationship("Bob", 0.6)  # Alice likes Bob
    bob_ai.set_relationship("Charlie", -0.3)  # Bob dislikes Charlie
    charlie_ai.set_relationship("Bob", -0.5)  # Charlie dislikes Bob
    alice_ai.set_relationship("Charlie", 0.2)  # Alice is neutral toward Charlie
    charlie_ai.set_relationship("Alice", 0.7)  # Charlie likes Alice

    # Test relationship queries
    logging.info(f"Bob's relationship with Alice: {bob_ai.relationships.get('Alice', 0)}")
    logging.info(f"Alice's relationship with Bob: {alice_ai.relationships.get('Bob', 0)}")
    logging.info(f"Bob's relationship with Charlie: {bob_ai.relationships.get('Charlie', 0)}")

    # Test emotional changes
    logging.info("--- Testing emotional changes ---")
    bob_ai.adjust_emotion("joy", 0.5)
    bob_ai.adjust_emotion("anger", 0.2)
    alice_ai.adjust_emotion("fear", 0.7)
    charlie_ai.adjust_emotion("sadness", 0.6)

    logging.info(f"Bob's dominant emotion: {bob_ai.get_dominant_emotion()}")
    logging.info(f"Alice's dominant emotion: {alice_ai.get_dominant_emotion()}")
    logging.info(f"Charlie's dominant emotion: {charlie_ai.get_dominant_emotion()}")

    # Test social event
    logging.info("--- Testing social event ---")
    bob_ai.adjust_relationship("Alice", 0.1)  # Improve relationship
    alice_ai.adjust_emotion("joy", 0.2)  # Alice becomes happier

    logging.info(f"Bob's updated relationship with Alice: {bob_ai.relationships.get('Alice', 0)}")
    logging.info(f"Alice's updated dominant emotion: {alice_ai.get_dominant_emotion()}")

def main():
    """Run all tests."""
    logging.info("Starting AI system tests...")

    test_behavior_tree()
    test_goap()
    test_scheduling()
    test_social()

    logging.info("All tests completed.")

if __name__ == "__main__":
    main()
