#!/usr/bin/env python3
"""
Test script for the world editor functionality.
This script tests various aspects of the world editor without launching the GUI.
"""

import os
import sys
import logging
import random
import pygame
import tempfile
import json
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("WorldEditorTest")

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Initialize pygame (needed for Surface creation)
pygame.init()

# Import modules from the game
try:
    from world_editor import get_editor_data, TerrainGenerator
    from data_loader import DataLoader
    from world.terrain_generator import TerrainGenerator as GameTerrainGenerator
    from world.biome_manager import BiomeManager
    logger.info("Successfully imported game modules")
except ImportError as e:
    logger.error(f"Failed to import game modules: {e}")
    sys.exit(1)

class WorldEditorTester:
    """Test class for the world editor functionality."""

    def __init__(self):
        self.test_results = {
            "data_loading": False,
            "terrain_generation": False,
            "biome_colors": False,
            "save_preview": False,
            "chunk_generation": False
        }
        self.temp_files = []

    def cleanup(self):
        """Clean up any temporary files created during testing."""
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"Removed temporary file: {file_path}")
            except Exception as e:
                logger.warning(f"Failed to remove temporary file {file_path}: {e}")

    def test_data_loading(self):
        """Test that data loading works correctly."""
        logger.info("Testing data loading...")

        try:
            # Test direct data loading
            loader = DataLoader()
            direct_data = loader.load_all_data()

            # Test editor data loading
            editor_data = get_editor_data()

            # Check that both methods load the same data
            direct_biomes = set(direct_data.get("biomes", {}).keys())
            editor_biomes = set(editor_data.get("biomes", {}).keys())

            if not direct_biomes:
                logger.error("Direct data loading failed: No biomes found")
                return False

            if not editor_biomes:
                logger.error("Editor data loading failed: No biomes found")
                return False

            # Check that the editor data contains all biomes from direct loading
            missing_biomes = direct_biomes - editor_biomes
            if missing_biomes:
                logger.warning(f"Editor data is missing biomes: {missing_biomes}")

            # Check color palette
            direct_palette = direct_data.get("color_palette", {}).get("biomes", {})
            editor_palette = editor_data.get("color_palette", {}).get("biomes", {})

            if not direct_palette:
                logger.warning("Direct data loading: No color palette found")

            if not editor_palette:
                logger.warning("Editor data loading: No color palette found")

            # Check that all biomes have colors in the editor palette
            missing_colors = [biome for biome in editor_biomes if biome not in editor_palette]
            if missing_colors:
                logger.warning(f"Biomes missing colors in editor palette: {missing_colors}")

            logger.info(f"Data loading test completed. Found {len(editor_biomes)} biomes.")
            self.test_results["data_loading"] = True
            return True

        except Exception as e:
            logger.error(f"Data loading test failed: {e}")
            return False

    def test_terrain_generation(self):
        """Test that terrain generation works correctly."""
        logger.info("Testing terrain generation...")

        try:
            # Get the editor data for biome_data and color_palette
            editor_data = get_editor_data()
            biome_data = editor_data.get("biomes", {})
            color_palette = editor_data.get("color_palette", {})

            # Create a terrain generator with a fixed seed
            seed = random.randint(1000, 9999999)
            chunk_size = 32

            # Test the game's terrain generator
            game_generator = GameTerrainGenerator(
                biome_data=biome_data,
                color_palette=color_palette,
                map_width=300,
                map_height=300,
                chunk_size=chunk_size,
                seed=seed
            )

            # Generate heightmap and temperature maps with explicit rotation
            game_generator.generate_heightmap()
            game_generator.generate_temperature_map(rotation_deg=0)  # Use 0 degrees rotation to avoid None
            game_generator.generate_rainfall_map(rotation_deg=0)  # Use 0 degrees rotation to avoid None

            # Generate a small region
            region_x, region_y = 0, 0
            game_chunk = game_generator.generate_world_chunk(region_x, region_y)

            if not game_chunk or "tiles" not in game_chunk:
                logger.error("Game terrain generator failed to generate a valid chunk")
                return False

            # Check the chunk structure
            tiles = game_chunk.get("tiles", [])
            if not tiles or not isinstance(tiles, list) or not tiles[0]:
                logger.error(f"Invalid chunk structure: {game_chunk.keys()}")
                return False

            # Check that tiles have biomes
            sample_tile = tiles[0][0]
            if "biome" not in sample_tile:
                logger.error(f"Tiles missing biome information: {sample_tile.keys()}")
                return False

            logger.info(f"Terrain generation test completed. Generated a {len(tiles)}x{len(tiles[0])} chunk.")
            self.test_results["terrain_generation"] = True
            return True

        except Exception as e:
            logger.error(f"Terrain generation test failed: {e}")
            return False

    def test_biome_colors(self):
        """Test that all biomes have colors and fallbacks work."""
        logger.info("Testing biome colors...")

        try:
            # Get the editor data
            editor_data = get_editor_data()
            biomes_data = editor_data.get("biomes", {})
            color_palette = editor_data.get("color_palette", {})

            # Check that we have biomes and a color palette
            if not biomes_data:
                logger.error("No biomes data found")
                return False

            if not color_palette:
                logger.error("No color palette found")
                return False

            # Add fallback colors if missing
            if "fallbacks" not in color_palette:
                color_palette["fallbacks"] = {
                    "unknown_biome": "#808080",
                    "missing_resource": "#FFFF00",
                    "error": "#FF0000"
                }

            # Ensure all biomes have colors
            if "biomes" not in color_palette:
                color_palette["biomes"] = {}

            # Generate colors for any missing biomes
            for biome_name in biomes_data.keys():
                if biome_name not in color_palette.get("biomes", {}):
                    # Generate a deterministic color based on the biome name
                    import hashlib
                    hash_val = int(hashlib.md5(biome_name.encode()).hexdigest(), 16)
                    r = (hash_val & 0xFF0000) >> 16
                    g = (hash_val & 0x00FF00) >> 8
                    b = hash_val & 0x0000FF
                    color_palette["biomes"][biome_name] = f"#{r:02x}{g:02x}{b:02x}"
                    logger.info(f"Generated color for missing biome: {biome_name} -> {color_palette['biomes'][biome_name]}")

            # Check that all biomes now have colors
            missing_colors = [biome for biome in biomes_data.keys() if biome not in color_palette.get("biomes", {})]
            if missing_colors:
                logger.error(f"Biomes still missing colors after generation: {missing_colors}")
                return False

            logger.info(f"Biome colors test completed. All {len(biomes_data)} biomes have colors.")
            self.test_results["biome_colors"] = True
            return True

        except Exception as e:
            logger.error(f"Biome colors test failed: {e}")
            return False

    def test_save_preview(self):
        """Test that save preview functionality works."""
        logger.info("Testing save preview functionality...")

        try:
            # Get the editor data for biome_data and color_palette
            editor_data = get_editor_data()
            biome_data = editor_data.get("biomes", {})
            color_palette = editor_data.get("color_palette", {})

            # Create a terrain generator with a fixed seed
            seed = random.randint(1000, 9999999)
            chunk_size = 32

            # Create a terrain generator
            game_generator = GameTerrainGenerator(
                biome_data=biome_data,
                color_palette=color_palette,
                map_width=300,
                map_height=300,
                chunk_size=chunk_size,
                seed=seed
            )

            # Generate heightmap and temperature maps with explicit rotation
            game_generator.generate_heightmap()
            game_generator.generate_temperature_map(rotation_deg=0)  # Use 0 degrees rotation to avoid None
            game_generator.generate_rainfall_map(rotation_deg=0)  # Use 0 degrees rotation to avoid None

            # Generate a small region
            region_x, region_y = 0, 0
            chunk = game_generator.generate_world_chunk(region_x, region_y)

            # Create a surface for the preview
            tiles = chunk.get("tiles", [])
            h = len(tiles)
            w = len(tiles[0]) if h else 0

            if h == 0 or w == 0:
                logger.error("Cannot save preview: No tiles to render")
                return False

            # Calculate cell size
            max_px = 512  # Smaller for testing
            cell = max(1, min(max_px // w if w else max_px, max_px // h if h else max_px))

            # Create a surface for the preview
            surf = pygame.Surface((w * cell, h * cell))
            surf.fill((0, 0, 0))  # Fill with black background

            # Add Ocean biome color if missing
            if "Ocean" not in color_palette.get("biomes", {}):
                color_palette.setdefault("biomes", {})["Ocean"] = "#0077BE"  # Standard ocean blue

            # Draw biomes
            missing_biomes = set()  # Track missing biomes to avoid duplicate warnings
            for y, row in enumerate(tiles):
                for x, tile in enumerate(row):
                    biome = tile.get("biome")
                    if biome not in color_palette.get("biomes", {}):
                        # Use fallback color
                        hex_color = color_palette.get("fallbacks", {}).get("unknown_biome", "#808080")

                        # Only log the first occurrence of each missing biome
                        if biome not in missing_biomes:
                            missing_biomes.add(biome)
                            logger.warning(f"Tile biome '{biome}' missing from palette! Using fallback color.")
                    else:
                        hex_color = color_palette["biomes"][biome]

                    # Convert hex color to RGB
                    try:
                        r = int(hex_color[1:3], 16)
                        g = int(hex_color[3:5], 16)
                        b = int(hex_color[5:7], 16)
                        color = (r, g, b)
                    except Exception:
                        color = (128, 128, 128)  # Fallback gray

                    pygame.draw.rect(surf, color, (x * cell, y * cell, cell, cell))

            # Save the preview to a temporary file
            temp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
            temp_file.close()
            self.temp_files.append(temp_file.name)

            pygame.image.save(surf, temp_file.name)

            # Check that the file was created and has content
            if not os.path.exists(temp_file.name):
                logger.error(f"Preview file was not created: {temp_file.name}")
                return False

            if os.path.getsize(temp_file.name) == 0:
                logger.error(f"Preview file is empty: {temp_file.name}")
                return False

            logger.info(f"Save preview test completed. Preview saved to {temp_file.name}")
            self.test_results["save_preview"] = True
            return True

        except Exception as e:
            logger.error(f"Save preview test failed: {e}")
            return False

    def test_chunk_generation(self):
        """Test chunk generation with different parameters."""
        logger.info("Testing chunk generation with different parameters...")

        try:
            # Get the editor data for biome_data and color_palette
            editor_data = get_editor_data()
            biome_data = editor_data.get("biomes", {})
            color_palette = editor_data.get("color_palette", {})

            # Test different seeds and chunk sizes
            test_configs = [
                {"seed": 12345, "size": 32},
                {"seed": 67890, "size": 64},
                {"seed": 54321, "size": 128}
            ]

            for config in test_configs:
                seed = config["seed"]
                size = config["size"]

                logger.info(f"Testing chunk generation with seed={seed}, size={size}")

                # Create a terrain generator
                generator = GameTerrainGenerator(
                    biome_data=biome_data,
                    color_palette=color_palette,
                    map_width=300,
                    map_height=300,
                    chunk_size=size,
                    seed=seed
                )

                # Generate heightmap and temperature maps with explicit rotation
                generator.generate_heightmap()
                generator.generate_temperature_map(rotation_deg=0)  # Use 0 degrees rotation to avoid None
                generator.generate_rainfall_map(rotation_deg=0)  # Use 0 degrees rotation to avoid None

                # Generate chunks at different coordinates
                for x, y in [(0, 0), (1, 0), (0, 1), (1, 1)]:
                    chunk = generator.generate_world_chunk(x, y)

                    # Verify chunk structure
                    if not chunk or "tiles" not in chunk:
                        logger.error(f"Invalid chunk at ({x}, {y})")
                        return False

                    tiles = chunk.get("tiles", [])
                    if not tiles or len(tiles) != size or len(tiles[0]) != size:
                        logger.error(f"Chunk size mismatch at ({x}, {y}): expected {size}x{size}, got {len(tiles)}x{len(tiles[0]) if tiles else 0}")
                        return False

                    # Check that tiles have required properties
                    sample_tile = tiles[size//2][size//2]  # Center tile
                    required_props = ["biome", "height", "temperature", "rainfall"]
                    missing_props = [prop for prop in required_props if prop not in sample_tile]

                    if missing_props:
                        logger.warning(f"Tiles missing properties: {missing_props}")

                    logger.info(f"Successfully generated chunk at ({x}, {y})")

            logger.info("Chunk generation test completed successfully")
            self.test_results["chunk_generation"] = True
            return True

        except Exception as e:
            logger.error(f"Chunk generation test failed: {e}")
            return False

    def run_all_tests(self):
        """Run all tests and report results."""
        logger.info("Starting world editor tests...")

        try:
            # Run all tests
            self.test_data_loading()
            self.test_terrain_generation()
            self.test_biome_colors()
            self.test_save_preview()
            self.test_chunk_generation()

            # Report results
            logger.info("=== TEST RESULTS ===")
            all_passed = True
            for test_name, result in self.test_results.items():
                status = "PASSED" if result else "FAILED"
                logger.info(f"{test_name}: {status}")
                if not result:
                    all_passed = False

            if all_passed:
                logger.info("All tests passed successfully!")
            else:
                logger.warning("Some tests failed. See log for details.")

            # Clean up
            self.cleanup()

            return all_passed

        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            self.cleanup()
            return False

if __name__ == "__main__":
    tester = WorldEditorTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
