# File: src/ui/combat_ui.py
"""
Combat UI for Living World RPG

Provides UI elements for displaying combat information:
- Health and stamina bars
- Combat status indicators
- Combat log display
"""

import pygame
import logging
from typing import Dict, List, Optional, Tuple

from ..core.entity import Entity
from ..core.components import CombatComponent


class HealthBar:
    """
    Health bar UI element for displaying entity health.
    """
    def __init__(self, x: int, y: int, width: int, height: int, 
                 border_color: Tuple[int, int, int] = (255, 255, 255),
                 bg_color: Tuple[int, int, int] = (64, 64, 64),
                 fill_color: Tuple[int, int, int] = (255, 0, 0)):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.border_color = border_color
        self.bg_color = bg_color
        self.fill_color = fill_color
        
    def draw(self, surface: pygame.Surface, current: int, maximum: int) -> None:
        """
        Draw the health bar.
        
        Args:
            surface: Pygame surface to draw on
            current: Current health value
            maximum: Maximum health value
        """
        # Draw border
        pygame.draw.rect(surface, self.border_color, 
                         (self.x, self.y, self.width, self.height), 1)
        
        # Draw background
        pygame.draw.rect(surface, self.bg_color, 
                         (self.x + 1, self.y + 1, self.width - 2, self.height - 2))
        
        # Calculate fill width
        if maximum <= 0:
            fill_width = 0
        else:
            fill_width = int((current / maximum) * (self.width - 2))
            
        # Draw fill
        if fill_width > 0:
            pygame.draw.rect(surface, self.fill_color, 
                             (self.x + 1, self.y + 1, fill_width, self.height - 2))


class StaminaBar(HealthBar):
    """
    Stamina bar UI element for displaying entity stamina.
    """
    def __init__(self, x: int, y: int, width: int, height: int):
        super().__init__(x, y, width, height, 
                         border_color=(255, 255, 255),
                         bg_color=(64, 64, 64),
                         fill_color=(0, 255, 0))  # Green for stamina


class CombatStatusIndicator:
    """
    Indicator for showing combat status (in combat, stunned, etc.).
    """
    def __init__(self, x: int, y: int, size: int = 10):
        self.x = x
        self.y = y
        self.size = size
        self.colors = {
            "normal": (64, 64, 64),      # Gray
            "in_combat": (255, 0, 0),    # Red
            "stunned": (255, 255, 0),    # Yellow
            "poisoned": (0, 255, 0),     # Green
            "frozen": (0, 255, 255),     # Cyan
            "burning": (255, 128, 0)     # Orange
        }
        
    def draw(self, surface: pygame.Surface, status: str) -> None:
        """
        Draw the status indicator.
        
        Args:
            surface: Pygame surface to draw on
            status: Status to display
        """
        color = self.colors.get(status, self.colors["normal"])
        pygame.draw.circle(surface, color, (self.x, self.y), self.size)
        pygame.draw.circle(surface, (255, 255, 255), (self.x, self.y), self.size, 1)


class CombatLogDisplay:
    """
    Display for showing combat log messages.
    """
    def __init__(self, x: int, y: int, width: int, height: int, 
                 font_size: int = 14, max_lines: int = 5):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.font_size = font_size
        self.max_lines = max_lines
        self.font = pygame.font.SysFont("Arial", font_size)
        self.messages: List[str] = []
        self.bg_color = (0, 0, 0, 128)  # Semi-transparent black
        
    def add_message(self, message: str) -> None:
        """
        Add a message to the combat log.
        
        Args:
            message: Message to add
        """
        self.messages.append(message)
        if len(self.messages) > self.max_lines:
            self.messages = self.messages[-self.max_lines:]
            
    def draw(self, surface: pygame.Surface) -> None:
        """
        Draw the combat log.
        
        Args:
            surface: Pygame surface to draw on
        """
        # Draw background
        s = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        s.fill(self.bg_color)
        surface.blit(s, (self.x, self.y))
        
        # Draw messages
        for i, message in enumerate(self.messages):
            text = self.font.render(message, True, (255, 255, 255))
            surface.blit(text, (self.x + 5, self.y + 5 + i * (self.font_size + 2)))


class EntityCombatDisplay:
    """
    Complete combat display for an entity, including health bar, stamina bar, and status.
    """
    def __init__(self, x: int, y: int, width: int, height: int, entity_name: str):
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.entity_name = entity_name
        
        # Create UI elements
        bar_height = 15
        self.health_bar = HealthBar(x + 5, y + 25, width - 10, bar_height)
        self.stamina_bar = StaminaBar(x + 5, y + 45, width - 10, bar_height)
        self.status_indicator = CombatStatusIndicator(x + width - 15, y + 15)
        
        # Create font for name
        self.font = pygame.font.SysFont("Arial", 16)
        
    def draw(self, surface: pygame.Surface, entity: Entity) -> None:
        """
        Draw the entity combat display.
        
        Args:
            surface: Pygame surface to draw on
            entity: Entity to display
        """
        # Draw background
        pygame.draw.rect(surface, (32, 32, 32), 
                         (self.x, self.y, self.width, self.height))
        pygame.draw.rect(surface, (255, 255, 255), 
                         (self.x, self.y, self.width, self.height), 1)
        
        # Draw name
        name_text = self.font.render(self.entity_name, True, (255, 255, 255))
        surface.blit(name_text, (self.x + 5, self.y + 5))
        
        # Get combat component
        combat_comp = entity.get_component(CombatComponent)
        if not combat_comp:
            return
            
        # Draw health bar
        self.health_bar.draw(surface, combat_comp.health, combat_comp.max_health)
        
        # Draw stamina bar
        self.stamina_bar.draw(surface, combat_comp.stamina, combat_comp.max_stamina)
        
        # Draw status indicator
        status = "in_combat" if combat_comp.in_combat else "normal"
        
        # Check for status effects
        if combat_comp.status_effects:
            for effect_name in combat_comp.status_effects:
                if effect_name in ["stunned", "poisoned", "frozen", "burning"]:
                    status = effect_name
                    break
                    
        self.status_indicator.draw(surface, status)
