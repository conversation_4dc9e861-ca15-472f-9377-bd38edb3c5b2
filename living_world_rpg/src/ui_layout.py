"""
UI layout utilities for responsive and non-overlapping UI elements
"""
import pygame

def clamp(value, min_value, max_value):
    """Clamp a value between min and max"""
    return max(min_value, min(value, max_value))

def get_safe_position(screen, x, y, width, height, padding=10):
    """
    Get a position that won't go off-screen
    Returns: (x, y, is_clamped)
    """
    screen_w, screen_h = screen.get_size()
    
    # Calculate safe bounds
    min_x = padding
    max_x = screen_w - width - padding
    min_y = padding
    max_y = screen_h - height - padding
    
    # Clamp position
    new_x = clamp(x, min_x, max_x)
    new_y = clamp(y, min_y, max_y)
    
    return new_x, new_y, (new_x != x or new_y != y)

def get_non_overlapping_position(screen, elements, x, y, width, height, padding=10):
    """
    Get a position that won't overlap with existing elements
    elements: list of (x, y, width, height) tuples
    Returns: (x, y, is_clamped)
    """
    screen_w, screen_h = screen.get_size()
    
    # Get safe position first
    x, y, _ = get_safe_position(screen, x, y, width, height, padding)
    
    # Check for overlaps and adjust if necessary
    for elem in elements:
        elem_x, elem_y, elem_w, elem_h = elem
        
        # If overlapping horizontally
        if (x < elem_x + elem_w + padding and 
            x + width > elem_x - padding and
            y < elem_y + elem_h + padding and
            y + height > elem_y - padding):
            
            # Try moving down first
            new_y = elem_y + elem_h + padding
            if new_y + height <= screen_h - padding:
                y = new_y
            else:
                # If can't move down, try moving right
                new_x = elem_x + elem_w + padding
                if new_x + width <= screen_w - padding:
                    x = new_x
                else:
                    # If can't move either way, stay in place
                    pass
    
    return x, y, True

def get_available_space(screen, elements, padding=10):
    """
    Get available space on screen not occupied by elements
    Returns: (available_width, available_height)
    """
    screen_w, screen_h = screen.get_size()
    total_width = screen_w - padding * 2
    total_height = screen_h - padding * 2
    
    for elem in elements:
        elem_x, elem_y, elem_w, elem_h = elem
        if elem_x >= padding and elem_x + elem_w <= screen_w - padding:
            total_width -= elem_w
        if elem_y >= padding and elem_y + elem_h <= screen_h - padding:
            total_height -= elem_h
    
    return total_width, total_height

def get_scaled_size(screen, width, height, max_ratio=0.9):
    """
    Scale dimensions to fit screen while maintaining aspect ratio
    Returns: (scaled_width, scaled_height)
    """
    screen_w, screen_h = screen.get_size()
    
    # Calculate max dimensions
    max_w = screen_w * max_ratio
    max_h = screen_h * max_ratio
    
    # Calculate scaling factors
    scale_x = max_w / width
    scale_y = max_h / height
    scale = min(scale_x, scale_y)
    
    return int(width * scale), int(height * scale)
