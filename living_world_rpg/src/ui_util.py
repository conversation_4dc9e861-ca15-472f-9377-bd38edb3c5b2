# File: src/ui_util.py
"""
UI utility functions for drawing text, buttons, and other UI elements.
Enhanced with caching and performance optimizations.
"""

import pygame
from typing import Tuple, Optional, List, Dict, Any
from utils.elastic_hash_table import ElasticHashTable

# Global caches for performance optimization
_text_cache: ElasticHashTable = ElasticHashTable(size=256)
_surface_cache: ElasticHashTable = ElasticHashTable(size=128)
_font_cache: Dict[Tuple[str, int], pygame.font.Font] = {}

# Performance tracking
_cache_hits = 0
_cache_misses = 0

def get_cached_font(font_name: Optional[str], size: int) -> pygame.font.Font:
    """Get a cached font or create and cache a new one."""
    global _font_cache
    key = (font_name or "default", size)
    if key not in _font_cache:
        if font_name:
            _font_cache[key] = pygame.font.Font(font_name, size)
        else:
            _font_cache[key] = pygame.font.Font(None, size)
    return _font_cache[key]

def clear_ui_caches():
    """Clear all UI caches to free memory."""
    global _text_cache, _surface_cache, _cache_hits, _cache_misses
    _text_cache.clear()
    _surface_cache.clear()
    _cache_hits = 0
    _cache_misses = 0

def get_cache_stats() -> Dict[str, Any]:
    """Get UI cache performance statistics."""
    total_requests = _cache_hits + _cache_misses
    hit_rate = (_cache_hits / total_requests * 100) if total_requests > 0 else 0
    return {
        'cache_hits': _cache_hits,
        'cache_misses': _cache_misses,
        'hit_rate': hit_rate,
        'text_cache_size': len(_text_cache),
        'surface_cache_size': len(_surface_cache),
        'font_cache_size': len(_font_cache)
    }

def draw_text(screen, font, txt, x, y, color=(255,255,255)):
    """Draw text with caching for improved performance."""
    global _text_cache, _cache_hits, _cache_misses

    # Create cache key from text properties
    cache_key = (txt, font.get_height(), color)

    # Check cache first
    cached_surface = _text_cache.get(cache_key)
    if cached_surface is not None:
        _cache_hits += 1
        txtsurf = cached_surface
    else:
        _cache_misses += 1
        txtsurf = font.render(txt, True, color)
        _text_cache.put(cache_key, txtsurf)

    txtrect = txtsurf.get_rect()
    txtrect.midleft = (x, y)
    screen.blit(txtsurf, txtrect)

def draw_titletext(screen, font, txt, x, y, color=(255,255,255)):
    surf = font.render(txt, True, color)
    rect = surf.get_rect(center=(x, y))
    screen.blit(surf, rect)

def draw_wrapped_text(screen, font, txt, x, y, max_width, color=(255,255,255)):
    words = txt.split(" ")
    lines = []
    current_line = ""
    for word in words:
        test_line = current_line + (" " if current_line != "" else "") + word
        if font.size(test_line)[0] <= max_width:
            current_line = test_line
        else:
            lines.append(current_line)
            current_line = word
    if current_line:
        lines.append(current_line)
    for i, line in enumerate(lines):
        draw_text(screen, font, line, x, y + i * (font.get_linesize() + 5), color)

def draw_multiline_text(screen, font, text, x, y, max_width, color=(255,255,255)):
    lines = text.split("\n")
    offset = 0
    for line in lines:
        draw_wrapped_text(screen, font, line, x, y + offset, max_width, color)
        offset += font.get_linesize()*2 + 9

# NEW: Scrollable Text Panel Function
def draw_scrollable_text_panel(screen, font, text, rect, scroll_offset=0, text_color=(255,255,255), bg_color=(50,50,50,200)):
    """
    Draws text inside a fixed rectangular panel.
    If the text height exceeds the rect.height, only the portion starting at scroll_offset is visible.
    Returns the total height of the rendered text (to allow scrollbar calculations).
    """
    # Create a temporary surface that is tall enough to hold all text.
    temp_surf = pygame.Surface((rect.width, 1000), pygame.SRCALPHA)
    temp_surf.fill((0,0,0,0))
    words = text.split(" ")
    lines = []
    current_line = ""
    max_text_width = rect.width - 20  # padding
    for word in words:
        test_line = current_line + (" " if current_line != "" else "") + word
        if font.size(test_line)[0] <= max_text_width:
            current_line = test_line
        else:
            lines.append(current_line)
            current_line = word
    if current_line:
        lines.append(current_line)
    line_height = font.get_linesize() + 5
    total_text_height = len(lines) * line_height
    for i, line in enumerate(lines):
        line_surf = font.render(line, True, text_color)
        temp_surf.blit(line_surf, (10, i * line_height))
    # Create the panel surface with a semi-transparent background.
    panel_surf = pygame.Surface((rect.width, rect.height), pygame.SRCALPHA)
    panel_surf.fill(bg_color)
    # Blit the text from the temporary surface onto the panel, offset by scroll_offset.
    panel_surf.blit(temp_surf, (0, -scroll_offset))
    screen.blit(panel_surf, (rect.x, rect.y))
    return total_text_height

def draw_input_box(screen, font, text, x, y, width, height, active):
    box_color = (200, 200, 200) if active else (255, 255, 255)
    pygame.draw.rect(screen, box_color, (x, y, width, height), border_radius=5)
    text_surf = font.render(text if text else "Enter seed...", True, (0,0,0))
    text_rect = text_surf.get_rect(midleft=(x + 10, y + height // 2))
    screen.blit(text_surf, text_rect)
    return pygame.Rect(x, y, width, height)

def draw_button(screen, font, text, x, y, width, height, color, hover):
    padding = 20
    text_surf = font.render(text, True, (255,255,255))
    text_width, text_height = text_surf.get_size()
    if width is None:
        width = text_width + padding
    if height is None:
        height = text_height + padding
    x = x - width // 2
    mouse_pos = pygame.mouse.get_pos()
    is_hovered = (x <= mouse_pos[0] <= x+width and y <= mouse_pos[1] <= y+height)
    current_color = hover if is_hovered else color
    button_rect = pygame.draw.rect(screen, current_color, (x, y, width, height), border_radius=5)
    text_rect = text_surf.get_rect(center=(x + width // 2, y + height // 2))
    screen.blit(text_surf, text_rect)
    return is_hovered, button_rect

def draw_tile_info(screen, font, tile_info, data, window_width, window_height):
    if not tile_info:
        return
    biome_name = tile_info.get("biome", "Unknown")
    biome_info = data["biomes"].get(biome_name, {})
    desc = biome_info.get("description", "No description.")
    text = f"Biome: {biome_name}\nDescription: {desc}"
    # Define a fixed rectangle for tile info display.
    info_rect = pygame.Rect(window_width - 350, 20, 330, 150)
    # Draw a semi-transparent background.
    bg_surf = pygame.Surface((info_rect.width, info_rect.height), pygame.SRCALPHA)
    bg_surf.fill((50,50,50,200))
    screen.blit(bg_surf, (info_rect.x, info_rect.y))
    # Render text inside the panel. (scroll_offset=0 for now; later we can add scroll control)
    draw_scrollable_text_panel(screen, font, text, info_rect, scroll_offset=0)

def draw_gathering_effect(screen, x, y, resource_type, color=(255, 255, 255), size=20):
    """Draw a visual effect when gathering resources"""
    import pygame
    import math

    # Draw expanding circle
    alpha = int(255 * (1 - size/40))  # Fade out as size increases
    surf = pygame.Surface((size*2, size*2), pygame.SRCALPHA)
    pygame.draw.circle(surf, (*color, alpha), (size, size), size)
    screen.blit(surf, (x - size, y - size))

def draw_item_details(screen, font, item_name, item_data, x, y, width, height, color=(255,255,255)):
    """Draw detailed item information including type, rarity, and description"""
    # Draw item background
    pygame.draw.rect(screen, (50,50,50), (x, y, width, height))

    # Item name in title case
    title = item_name.replace('_', ' ').title()
    draw_text(screen, font, title, x + 10, y + 10, color)

    # Draw rarity with appropriate color
    rarity = item_data.get("rarity", "common")
    rarity_colors = {
        "common": (200, 200, 200),
        "uncommon": (0, 255, 0),
        "rare": (0, 112, 221),
        "legendary": (163, 53, 238)
    }
    rarity_color = rarity_colors.get(rarity.lower(), (200,200,200))
    draw_text(screen, font, rarity.capitalize(), x + 10, y + 40, rarity_color)

    # Draw type
    item_type = item_data.get("type", "")
    draw_text(screen, font, f"Type: {item_type.capitalize()}", x + 10, y + 70, (200,200,200))

    # Draw description with word wrap
    desc = item_data.get("description", "No description available.")
    draw_wrapped_text(screen, font, desc, x + 10, y + 100, width - 20, (180,180,180))

    # Draw price if available
    price = item_data.get("base_price")
    if price is not None:
        draw_text(screen, font, f"Value: {price}", x + 10, y + height - 30, (255,215,0))

def draw_skill_bar(screen, font, skill_name, current_level, current_xp, xp_threshold, x, y, width=200, height=20):
    """Draw a skill progress bar"""
    # Draw background
    pygame.draw.rect(screen, (50,50,50), (x, y, width, height))

    # Draw progress
    progress = current_xp / xp_threshold
    progress_width = int(width * progress)
    pygame.draw.rect(screen, (0,255,0), (x, y, progress_width, height))

    # Draw border
    pygame.draw.rect(screen, (200,200,200), (x, y, width, height), 1)

    # Draw text
    text = f"{skill_name.capitalize()} (Lvl {current_level}) - {current_xp}/{xp_threshold}"
    draw_text(screen, font, text, x + 5, y + height//4, (255,255,255))

def draw_popup_message(screen, font, message, x, y, duration, max_duration, color=(255,255,255)):
    """Draw a floating message that fades out over time"""
    alpha = int(255 * (duration / max_duration))
    text_surface = font.render(message, True, color)
    text_surface.set_alpha(alpha)
    screen.blit(text_surface, (x, y))

def draw_tool_durability(screen, font, tool_name, durability, max_durability, x, y, width=100, height=10):
    """Draw a tool's durability bar"""
    # Draw background
    pygame.draw.rect(screen, (50,50,50), (x, y, width, height))

    # Draw durability bar
    progress = durability / max_durability
    progress_width = int(width * progress)

    # Color changes based on durability percentage
    if progress > 0.6:
        color = (0, 255, 0)  # Green
    elif progress > 0.3:
        color = (255, 165, 0)  # Orange
    else:
        color = (255, 0, 0)  # Red

    pygame.draw.rect(screen, color, (x, y, progress_width, height))
    pygame.draw.rect(screen, (200,200,200), (x, y, width, height), 1)

    # Draw text
    text = f"{tool_name}: {int(durability)}/{int(max_durability)}"
    text_surf = font.render(text, True, (255,255,255))
    text_rect = text_surf.get_rect(topleft=(x, y - 20))
    screen.blit(text_surf, text_rect)