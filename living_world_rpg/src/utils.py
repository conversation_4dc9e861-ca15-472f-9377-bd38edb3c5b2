"""Utility functions for the game."""

from typing import <PERSON><PERSON>, Optional
import pygame
from constants import COLOR_WHITE, COLOR_BLACK, COLOR_GREEN, COLOR_RED


def draw_text(
    screen: pygame.Surface,
    font: pygame.font.Font,
    text: str,
    x: int,
    y: int,
    color: Tuple[int, int, int] = COLOR_WHITE,
    align: str = "left"
) -> None:
    """Draw text on the screen with optional alignment.

    Args:
        screen: Surface to draw on
        font: Font to use
        text: Text to draw
        x: X coordinate
        y: Y coordinate
        color: Text color
        align: Alignment (left, center, right)
    """
    text_surface = font.render(text, True, color)
    text_rect = text_surface.get_rect()
    
    if align == "center":
        text_rect.center = (x, y)
    elif align == "right":
        text_rect.topright = (x, y)
    else:  # left
        text_rect.topleft = (x, y)
    
    screen.blit(text_surface, text_rect)

def draw_wrapped_text(
    screen: pygame.Surface,
    font: pygame.font.Font,
    text: str,
    x: int,
    y: int,
    max_width: int,
    color: Tuple[int, int, int] = COLOR_WHITE
) -> None:
    """Draw text that wraps within a specified width.

    Args:
        screen: Surface to draw on
        font: Font to use
        text: Text to draw
        x: X coordinate
        y: Y coordinate
        max_width: Maximum width before wrapping
        color: Text color
    """
    words = text.split()
    current_line = words[0]
    line_height = font.get_height()
    current_y = y

    for word in words[1:]:
        test_line = current_line + " " + word
        if font.size(test_line)[0] <= max_width:
            current_line = test_line
        else:
            draw_text(screen, font, current_line, x, current_y, color)
            current_line = word
            current_y += line_height

    draw_text(screen, font, current_line, x, current_y, color)

def draw_button(
    screen: pygame.Surface,
    font: pygame.font.Font,
    text: str,
    x: int,
    y: int,
    width: int = None,
    height: int = None,
    font_size: int = 30,
    color: Tuple[int, int, int] = COLOR_GREEN,
    hover_color: Tuple[int, int, int] = COLOR_RED
) -> Tuple[bool, pygame.Rect]:
    """Draw a button with hover effect.

    Args:
        screen: Surface to draw on
        font: Font to use
        text: Button text
        x: X coordinate
        y: Y coordinate
        width: Button width (optional)
        height: Button height (optional)
        font_size: Font size
        color: Normal color
        hover_color: Hover color

    Returns:
        Tuple[bool, pygame.Rect]: (is_hovered, button_rect)
    """
    if width is None:
        width = font.size(text)[0] + 20
    if height is None:
        height = font_size + 10

    button_rect = pygame.Rect(x, y, width, height)
    mouse_pos = pygame.mouse.get_pos()
    is_hovered = button_rect.collidepoint(mouse_pos)
    
    # Draw button background
    pygame.draw.rect(screen, hover_color if is_hovered else color, button_rect)
    pygame.draw.rect(screen, COLOR_BLACK, button_rect, 2)
    
    # Draw text
    text_surface = font.render(text, True, COLOR_WHITE)
    text_rect = text_surface.get_rect(center=button_rect.center)
    screen.blit(text_surface, text_rect)
    
    return is_hovered, button_rect

def get_safe_position(
    screen: pygame.Surface,
    x: int,
    y: int,
    width: int,
    height: int,
    padding: int = 10
) -> Tuple[int, int, bool]:
    """Get a position that's safe to draw within screen bounds.

    Args:
        screen: Surface to check against
        x: Desired X position
        y: Desired Y position
        width: Element width
        height: Element height
        padding: Padding around element

    Returns:
        Tuple[int, int, bool]: (adjusted_x, adjusted_y, was_adjusted)
    """
    screen_rect = screen.get_rect()
    element_rect = pygame.Rect(x, y, width, height)
    
    # Adjust if too far left
    if element_rect.left < padding:
        x = padding
    
    # Adjust if too far right
    if element_rect.right > screen_rect.right - padding:
        x = screen_rect.right - width - padding
    
    # Adjust if too high
    if element_rect.top < padding:
        y = padding
    
    # Adjust if too low
    if element_rect.bottom > screen_rect.bottom - padding:
        y = screen_rect.bottom - height - padding
    
    return x, y, x != element_rect.x or y != element_rect.y
