from .elastic_hash_table import ElasticHashTable


class ElasticDictAdapter:
    """
    Adapter class that provides a dict-like interface for ElasticHashTable.
    This makes migration easier by allowing drop-in replacement of dict objects.

    This implementation uses a hybrid approach that switches between Python's built-in
    dict and our custom ElasticHashTable based on the size and usage patterns.
    """

    def __init__(self, initial_data=None, size=128):
        """
        Initialize the adapter with an optional dictionary of initial data.

        Args:
            initial_data: Optional dictionary to initialize the hash table with
            size: Initial size of the hash table
        """
        # Use Python's built-in dict for small to medium tables (much faster)
        # Use our custom ElasticHashTable with Funnel Hashing for large tables (better memory usage)
        self.size_threshold = 2000  # Adjusted threshold based on Funnel Hashing performance

        # Start with a regular dict for maximum performance
        self._using_dict = True
        self._dict = {}
        self.table = None

        if initial_data:
            if len(initial_data) > self.size_threshold:
                self._using_dict = False
                self.table = ElasticHashTable(size, delta=0.25)
                for key, value in initial_data.items():
                    self.table.insert(key, value)
            else:
                self._dict = dict(initial_data)

    def _maybe_convert_to_table(self):
        """Convert from dict to ElasticHashTable if we exceed the size threshold."""
        if self._using_dict and len(self._dict) > self.size_threshold:
            self._using_dict = False
            self.table = ElasticHashTable(max(128, len(self._dict) * 2), delta=0.25)
            for k, v in self._dict.items():
                self.table.insert(k, v)
            self._dict = None  # Free memory

    def __getitem__(self, key):
        """Get an item using dictionary syntax."""
        if self._using_dict:
            return self._dict[key]  # Will raise KeyError if not found
        else:
            value = self.table.get(key)
            if value is None:
                raise KeyError(key)
            return value

    def __setitem__(self, key, value):
        """Set an item using dictionary syntax."""
        if self._using_dict:
            self._dict[key] = value
            self._maybe_convert_to_table()
        else:
            self.table.insert(key, value)

    def __delitem__(self, key):
        """Delete an item using dictionary syntax."""
        if self._using_dict:
            del self._dict[key]  # Will raise KeyError if not found
        else:
            if key not in self:
                raise KeyError(key)
            self.table.remove(key)

    def __contains__(self, key):
        """Check if key exists in the table."""
        if self._using_dict:
            return key in self._dict
        else:
            return self.table.get(key) is not None

    def __len__(self):
        """Return the number of items in the table."""
        if self._using_dict:
            return len(self._dict)
        else:
            return self.table.count

    def get(self, key, default=None):
        """Get a value with an optional default if key doesn't exist."""
        if self._using_dict:
            return self._dict.get(key, default)
        else:
            value = self.table.get(key)
            return value if value is not None else default

    def pop(self, key, default=None):
        """Remove and return a value, with an optional default if key doesn't exist."""
        if self._using_dict:
            return self._dict.pop(key, default)
        else:
            value = self.table.get(key)
            if value is None:
                if default is not None:
                    return default
                raise KeyError(key)
            self.table.remove(key)
            return value

    def update(self, other):
        """Update with key/value pairs from another dictionary."""
        if self._using_dict:
            self._dict.update(other)
            self._maybe_convert_to_table()
        else:
            if hasattr(other, 'items'):
                for key, value in other.items():
                    self.table.insert(key, value)
            else:
                for key, value in other:
                    self.table.insert(key, value)

    def items(self):
        """Return a list of (key, value) tuples for all items in the table."""
        if self._using_dict:
            return list(self._dict.items())
        else:
            return self.table.items()

    def keys(self):
        """Return a list of all keys in the table."""
        if self._using_dict:
            return list(self._dict.keys())
        else:
            return self.table.get_keys()

    def values(self):
        """Return a list of all values in the table."""
        if self._using_dict:
            return list(self._dict.values())
        else:
            return self.table.get_values()

    def clear(self):
        """Clear all items from the hash table."""
        if self._using_dict:
            self._dict.clear()
        else:
            size = self.table.size
            self.table = ElasticHashTable(size, delta=0.25)

    def setdefault(self, key, default=None):
        """Get a value or set a default if key doesn't exist."""
        if self._using_dict:
            return self._dict.setdefault(key, default)
        else:
            value = self.table.get(key)
            if value is None:
                self.table.insert(key, default)
                return default
            return value

    def copy(self):
        """Return a copy of the hash table."""
        new_adapter = ElasticDictAdapter()
        if self._using_dict:
            new_adapter._dict = self._dict.copy()
        else:
            new_adapter._using_dict = False
            new_adapter.table = ElasticHashTable(self.table.size, delta=self.table.delta)
            for k, v in self.table.items():
                new_adapter.table.insert(k, v)
        return new_adapter

    def to_dict(self):
        """Convert to a regular Python dictionary."""
        if self._using_dict:
            return dict(self._dict)
        else:
            return dict(self.table.items())

    def __iter__(self):
        """Return an iterator over the keys."""
        if self._using_dict:
            return iter(self._dict)
        else:
            return iter(self.table.get_keys())
