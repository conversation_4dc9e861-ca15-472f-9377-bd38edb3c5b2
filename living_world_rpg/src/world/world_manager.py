# File: src/world/world_manager.py
"""
World Manager for Living World RPG

- Coordinates the ECSWorld with terrain generation, biomes, settlements, events, and world history.
- Computes the current season, weather, and handles biome-specific adjustments.
- Initializes settlements, registers systems, and triggers world updates.
"""

import logging
import random
import time
import numpy as np
from pathlib import Path
from collections import Counter
from typing import Optional, Tuple, Dict, Any, List

from utils.elastic_dict_adapter import ElasticDictAdapter

from world.biome_manager import BiomeManager
from world.settlement_manager import SettlementManager
from core.components import PositionComponent, CreatureComponent
from world.economy_manager import EconomyManager
from world.event_manager import EventManager
from world.faction_manager import FactionManager
from world.resource_manager import ResourceManager
from world.terrain_generator import TerrainGenerator
from world.chunk_manager import ChunkManager
from world.world_renderer import WorldRenderer, RenderSettings
from world.world_history import WorldHistory
# System imports moved to local scope to avoid circular imports
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from ..core.systems import (
        ResourceProductionSystem, ResourceConsumptionSystem,
        FactionBehaviorSystem, EconomySystem, CameraSystem, CombatSystem,
        MovementSystem, AISystem, LevelingSystem, CraftingSystem, GatheringSystem
    )
    from ..core.components import (
        SettlementComponent, InventoryComponent, 
        CameraComponent, StatsComponent
    )

logging.basicConfig(level=logging.INFO)

class WorldManager:
    def __init__(self, ecs_world, data: dict):
        self.ecs_world = ecs_world
        self.biome_data = data.get("biomes", {})
        self.settlements_data = data.get("settlements", {})
        self.faction_data = data.get("factions", {})
        self.status_effects = "status_effects"

        # Initialize terrain generator with proper color palette
        if "color_palette" not in data:
            raise ValueError("[WorldManager] Missing color palette in data")

        # Initialize terrain generator with larger world size
        self.map_width = 1000
        self.map_height = 1000
        self.chunk_size = 32  # Size of each chunk in tiles
        
        self.terrain_generator = TerrainGenerator(
            biome_data=self.biome_data,
            color_palette=data["color_palette"],
            map_width=self.map_width,
            map_height=self.map_height,
            chunk_size=self.chunk_size,
            seed=random.randint(0, 9999999),
            edge_fade_strength=0.3,
            sea_level=0.4
        )
        
        # Initialize chunk manager
        self.chunk_manager = ChunkManager(
            world_dir=os.path.join("saves", "chunks"),
            chunk_size=self.chunk_size,
            cache_size=100  # Number of chunks to keep in memory
        )
        
        # Initialize world renderer with LOD settings
        self.world_renderer = WorldRenderer(
            chunk_manager=self.chunk_manager,
            settings=RenderSettings(
                tile_size=32,  # Base tile size for rendering
                lod0_threshold=1.0,  # Full detail when zoomed in close
                lod1_threshold=0.5,  # Medium detail at medium zoom
                lod2_threshold=0.25,  # Low detail when zoomed out
                view_padding=2  # Number of chunks to render outside the view
            )
        )
        
        # Initialize chunk data structure
        self.chunk_data = {}
        self.generated_chunks = set()  # Track which chunks have been generated

        # Initialize managers first
        self.biome_manager = BiomeManager(self.biome_data)
        self.faction_manager = FactionManager(self.faction_data)
        self.resource_manager = ResourceManager(self.biome_data, self.settlements_data, self.faction_data)
        self.event_manager = EventManager(data.get("event_thresholds", {}))
        self.economy_manager = EconomyManager(self.ecs_world, data.get("items", {}), self.faction_manager)
        self.starting_biome = None
        
        # Initialize systems with local imports to avoid circular imports
        from core.systems import (
            ResourceProductionSystem, ResourceConsumptionSystem,
            FactionBehaviorSystem, EconomySystem, CameraSystem, CombatSystem,
            MovementSystem, AISystem, LevelingSystem, CraftingSystem, GatheringSystem
        )
        
        # Initialize systems after all required managers are set up
        self.ecs_world.add_system(ResourceProductionSystem(
            biome_data=self.biome_data,
            seasonal_data=data.get("seasons", {}),
            resource_manager=self.resource_manager,
            faction_data=self.faction_data,
            special_conditions=data.get("special_conditions", {}),
            event_thresholds=data.get("event_thresholds", {}),
            weather_data=data.get("weather", {})
        ))
        
        # Generate initial world if needed
        self._generate_initial_world()
        
        # Add other systems
        self.ecs_world.add_system(ResourceConsumptionSystem(self.resource_manager))
        
        # Set up camera system with world bounds
        self.ecs_world.add_system(CameraSystem(
            world_width=self.map_width * self.chunk_size,
            world_height=self.map_height * self.chunk_size
        ))
        self.ecs_world.add_system(FactionBehaviorSystem())
        self.ecs_world.add_system(EconomySystem(data.get("items", {}), self.economy_manager))
        self.ecs_world.add_system(CameraSystem(
            world_width=self.terrain_generator.map_width * 32,  # Assuming 32x32 tiles
            world_height=self.terrain_generator.map_height * 32
        ))
        self.ecs_world.add_system(CombatSystem())
        self.ecs_world.add_system(MovementSystem())
        self.ecs_world.add_system(AISystem())
        self.ecs_world.add_system(LevelingSystem())
        self.ecs_world.add_system(CraftingSystem())
        self.ecs_world.add_system(GatheringSystem())

        # Region management
        self.active_regions = ElasticDictAdapter(size=64)  # Dictionary of loaded regions
        self.simulation_radius = 2  # Number of regions to simulate around player
        self.current_region = (0, 0)  # Current player region coordinates
        self.regions_to_load = set()  # Regions scheduled for loading
        self.regions_loading = set()  # Regions currently being loaded

        # Initialize world map
        self.world_map = {"tiles": []}  # Placeholder for aggregated map data
        
        # World generation settings
        self.world_seed = random.randint(0, 2**32 - 1)
        self.biome_map = None
        self.temperature_map = None
        self.rainfall_map = None
        self.height_map = None

        logging.info("[WorldManager] Initialized with region-based world system")

    def _generate_initial_world(self) -> None:
        """
        Generate the initial world data using the terrain generator and chunk manager.
        This creates the base terrain, biomes, and environmental maps.
        """
        logging.info(f"[WorldManager] Generating initial world with seed: {self.world_seed}")
        
        try:
            # Generate the full world maps
            self.biome_map, self.temperature_map, self.rainfall_map, self.height_map = \
                self.terrain_generator.generate_clustered_biome_map(
                    width=self.map_width,
                    height=self.map_height,
                    seed=self.world_seed,
                    chunk_size=self.chunk_size
                )
            
            # Process the world in chunks and save to chunk manager
            chunk_count = 0
            for chunk_y in range(0, self.map_height, self.chunk_size):
                for chunk_x in range(0, self.map_width, self.chunk_size):
                    # Calculate chunk bounds
                    y_end = min(chunk_y + self.chunk_size, self.map_height)
                    x_end = min(chunk_x + self.chunk_size, self.map_width)
                    
                    # Extract chunk data
                    chunk_biome = self.biome_map[chunk_y:y_end, chunk_x:x_end]
                    chunk_temp = self.temperature_map[chunk_y:y_end, chunk_x:x_end]
                    chunk_rain = self.rainfall_map[chunk_y:y_end, chunk_x:x_end]
                    chunk_height = self.height_map[chunk_y:y_end, chunk_x:x_end]
                    
                    # Create chunk data with multiple LODs
                    chunk_data = ChunkData()
                    chunk_data.lod0 = chunk_biome  # Full detail
                    
                    # Create lower LODs by downsampling
                    if chunk_biome.shape[0] > 1 and chunk_biome.shape[1] > 1:
                        # Medium detail (half resolution)
                        chunk_data.lod1 = chunk_biome[::2, ::2]
                        
                        # Low detail (quarter resolution)
                        if chunk_biome.shape[0] > 2 and chunk_biome.shape[1] > 2:
                            chunk_data.lod2 = chunk_biome[::4, ::4]
                    
                    # Save chunk to disk
                    self.chunk_manager.save_chunk(
                        chunk_x // self.chunk_size,
                        chunk_y // self.chunk_size,
                        chunk_data
                    )
                    chunk_count += 1
            
            logging.info(f"[WorldManager] Generated {chunk_count} chunks")
            
        except Exception as e:
            logging.error(f"[WorldManager] Error generating world: {e}")
            raise

    def init_world(self) -> None:
        logging.info("[WorldManager] Initializing game world.")

        if not self.starting_biome:
            self.starting_biome = "Molten Fields"  # Default biome
            logging.warning(f"[WorldManager] No starting biome set, defaulting to {self.starting_biome}")

        # Set the biome in terrain generator
        self.terrain_generator.set_starting_biome(self.starting_biome)

        # Generate initial region (center region of 5x5 grid)
        center = self.terrain_generator.region_grid_size // 2
        self.load_region(center, center)

        # --- Refactored aggregation: build a 2D world array ---
        region = self.active_regions.get((center, center))
        if region and "tiles" in region:
            self.world_map["tiles"] = [row[:] for row in region["tiles"]]
        else:
            self.world_map["tiles"] = []

        # Find a settlement in the chosen biome
        player_position = self.find_settlement_in_biome(self.starting_biome)
        if not player_position:
            # Fallback: use center region's player_position
            center_region = self.active_regions.get((center, center), {})
            player_position = center_region.get("player_position", {"x": 0, "y": 0})

        # Update player position
        player = self.ecs_world.get_player_entity()
        if player:
            pos_comp = player.get_component(PositionComponent)
            if pos_comp:
                pos_comp.x = player_position["x"]
                pos_comp.y = player_position["y"]
            else:
                player.add_component(PositionComponent(
                    player_position["x"],
                    player_position["y"]
                ))

        # Initialize starting region content
        center_region = self.active_regions.get((center, center), {})
        self._initialize_region_content(center_region, self.starting_biome)

        logging.info(f"[WorldManager] World initialized with starting biome: {self.starting_biome}")

    def find_settlement_in_biome(self, biome_name: str):
        """Return the position of a settlement in the given biome, or None if not found."""
        # First try to find a settlement in the specified biome
        for region in self.active_regions.values():
            for row in region.get("tiles", []):
                for tile in row:
                    if tile.get("settlement") and tile.get("biome") == biome_name:
                        return {"x": tile["x"], "y": tile["y"]}

        # If no settlement found in the specified biome, find any non-ocean tile in that biome
        for region in self.active_regions.values():
            for row in region.get("tiles", []):
                for tile in row:
                    if tile.get("biome") == biome_name and tile.get("biome") != "Ocean":
                        logging.info(f"[WorldManager] No settlement found in {biome_name}, using non-settlement tile at ({tile['x']}, {tile['y']})")
                        return {"x": tile["x"], "y": tile["y"]}

        # If no tile of the specified biome found, find any non-ocean tile
        for region in self.active_regions.values():
            for row in region.get("tiles", []):
                for tile in row:
                    if tile.get("biome") != "Ocean":
                        logging.warning(f"[WorldManager] No {biome_name} tiles found, using {tile.get('biome')} tile at ({tile['x']}, {tile['y']})")
                        return {"x": tile["x"], "y": tile["y"]}

        # Last resort: use the center of the map
        center = self.terrain_generator.region_size * self.terrain_generator.region_grid_size // 2
        logging.warning(f"[WorldManager] No suitable spawn location found, using center coordinates ({center}, {center})")
        return {"x": center, "y": center}

    def _initialize_region_content(self, region: dict, primary_biome: str) -> None:
        """Initialize content for a specific region"""
        if not region or "tiles" not in region:
            return

        biome_info = self.biome_data.get(primary_biome, {})
        if biome_info:
            self.adjust_terrain_based_on_biome(primary_biome, biome_info, region["tiles"])
            self.spawn_biome_creatures(primary_biome, biome_info, region["tiles"])
            self.distribute_biome_resources(primary_biome, biome_info, region["tiles"])
            self.setup_biome_events(primary_biome, biome_info)

    def load_region(self, region_x: int, region_y: int) -> None:
        """Load a specific region into memory with error handling and feedback"""
        try:
            # Check if already loaded
            if (region_x, region_y) in self.active_regions:
                return
                
            # Generate the region
            new_region = self.terrain_generator.generate_world_chunk(region_x, region_y)
            if not new_region or "tiles" not in new_region:
                logging.error(f"[WorldManager] Failed to generate region ({region_x}, {region_y})")
                return
                
            # Store the region
            self.active_regions[(region_x, region_y)] = new_region
            
            # Find primary biome for initialization
            try:
                biome_counts = Counter(
                    tile["biome"] for row in new_region["tiles"] for tile in row
                )
                if biome_counts:
                    primary_biome = max(biome_counts.items(), key=lambda x: x[1])[0]
                    self._initialize_region_content(new_region, primary_biome)
                    
                logging.info(f"[WorldManager] Loaded region ({region_x}, {region_y}) with {len(new_region['tiles'])}x{len(new_region['tiles'][0])} tiles")
                
            except Exception as e:
                logging.error(f"[WorldManager] Error initializing region ({region_x}, {region_y}): {str(e)}")
                
        except Exception as e:
            logging.error(f"[WorldManager] Critical error loading region ({region_x}, {region_y}): {str(e)}")
            # Re-raise to allow upper layers to handle the error
            raise

    def unload_region(self, region_x: int, region_y: int, save_to_disk: bool = False) -> None:
        """Unload a region from memory with optional disk persistence
        
        Args:
            region_x: X coordinate of the region to unload
            region_y: Y coordinate of the region to unload
            save_to_disk: If True, save the region state to disk before unloading
        """
        region_key = (region_x, region_y)
        if region_key in self.active_regions:
            try:
                region = self.active_regions[region_key]
                
                # Save region state if needed
                if save_to_disk and 'modified' in region and region['modified']:
                    self._save_region_to_disk(region_x, region_y, region)
                
                # Clean up any resources
                if 'entities' in region:
                    for entity_id in region['entities']:
                        if entity_id in self.ecs_world.entities:
                            self.ecs_world.remove_entity(entity_id)
                
                # Remove from active regions
                del self.active_regions[region_key]
                logging.info(f"[WorldManager] Unloaded region ({region_x}, {region_y})")
                
            except Exception as e:
                logging.error(f"[WorldManager] Error unloading region ({region_x}, {region_y}): {str(e)}")
                raise

    def _save_region_to_disk(self, region_x: int, region_y: int, region_data: dict) -> bool:
        """
        Save a region's data to disk for persistence.
        
        Args:
            region_x: X coordinate of the region
            region_y: Y coordinate of the region
            region_data: The region data to save
            
        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            # Create save directory if it doesn't exist
            save_dir = Path("saved_regions")
            save_dir.mkdir(exist_ok=True)
            
            # Create a simplified version of the region for saving
            save_data = {
                'region_x': region_x,
                'region_y': region_y,
                'tile_count': sum(len(row) for row in region_data.get('tiles', [])),
                'last_saved': time.time(),
                'metadata': {}
            }
            
            # Save entities if present
            if 'entities' in region_data:
                save_data['entity_count'] = len(region_data['entities'])
                # TODO: Add entity serialization logic here
                
            # Save to JSON file
            save_path = save_dir / f"region_{region_x}_{region_y}.json"
            with open(save_path, 'w') as f:
                import json
                json.dump(save_data, f, indent=2)
                
            logging.info(f"[WorldManager] Saved region ({region_x}, {region_y}) to {save_path}")
            return True
            
        except Exception as e:
            logging.error(f"[WorldManager] Error saving region ({region_x}, {region_y}): {str(e)}")
            return False

    def update_active_regions(self, player_x: float, player_y: float) -> None:
        """Update which regions are active based on player position"""
        # Calculate current region
        region_size = self.terrain_generator.region_size
        new_region_x = int(player_x) // region_size
        new_region_y = int(player_y) // region_size

        # Always update the current region
        self.current_region = (new_region_x, new_region_y)

        # Determine regions to load/unload
        needed_regions = set()
        for dx in range(-self.simulation_radius, self.simulation_radius + 1):
            for dy in range(-self.simulation_radius, self.simulation_radius + 1):
                rx, ry = new_region_x + dx, new_region_y + dy
                if 0 <= rx < self.terrain_generator.region_grid_size and \
                   0 <= ry < self.terrain_generator.region_grid_size:
                    needed_regions.add((rx, ry))

        # Schedule regions for loading
        for region_coord in needed_regions:
            if (region_coord not in self.active_regions and 
                region_coord not in self.regions_to_load and
                region_coord not in self.regions_loading):
                self.regions_to_load.add(region_coord)

        # Unload regions that are too far away
        regions_to_unload = []
        for region_coord in self.active_regions.keys():
            if region_coord not in needed_regions:
                regions_to_unload.append(region_coord)
        
        # Unload regions in a separate pass to avoid modifying the dict during iteration
        for region_coord in regions_to_unload:
            self.unload_region(*region_coord)
        
        # Process a limited number of region loads per frame
        self._process_region_loading()
    
    def _process_region_loading(self):
        """Process a limited number of region loads per frame"""
        max_loads_per_frame = 1  # Limit to prevent stuttering
        loaded_this_frame = 0
        
        while self.regions_to_load and loaded_this_frame < max_loads_per_frame:
            region_coord = self.regions_to_load.pop()
            if region_coord not in self.active_regions:
                self.regions_loading.add(region_coord)
                self.load_region(*region_coord)
                self.regions_loading.discard(region_coord)
                loaded_this_frame += 1

    def get_tile_global(self, x: int, y: int) -> Optional[dict]:
        """Retrieve a tile based on global coordinates using active regions."""
        region_size = self.terrain_generator.region_size
        rx = x // region_size
        ry = y // region_size
        region = self.active_regions.get((rx, ry))
        if not region or "tiles" not in region:
            return None
        lx = x % region_size
        ly = y % region_size
        if ly < len(region["tiles"]) and lx < len(region["tiles"][0]):
            return region["tiles"][ly][lx]
        return None

    def adjust_terrain_based_on_biome(self, biome_name: str, biome_info: dict, tiles: list) -> None:
        """Add biome-specific terrain features to tiles"""
        modifiers = biome_info.get("terrain_modifiers", {})
        veg_chance = modifiers.get("add_vegetation_chance", 0)
        rock_chance = modifiers.get("add_rocky_terrain_chance", 0)
        for row in tiles:
            for tile in row:
                if tile["biome"] == biome_name:
                    if random.random() < veg_chance:
                        tile["vegetation"] = "Dense Vegetation"
                    if random.random() < rock_chance:
                        tile["terrain_feature"] = "Rocky Outcrop"

    def spawn_biome_creatures(self, biome_name: str, biome_info: dict, tiles: list) -> None:
        """Spawn creatures in appropriate tiles within the region"""
        creatures = biome_info.get("creatures", [])
        suitable_tiles = [(t["x"], t["y"]) for row in tiles for t in row if t["biome"] == biome_name]

        if not suitable_tiles:
            return

        for creature in creatures:
            count = creature.get("initial_count", 5)
            for _ in range(count):
                x, y = random.choice(suitable_tiles)
                self.spawn_creature_at_position(creature, x, y)

    def spawn_creature_at_position(self, creature_data: dict, x: int, y: int) -> None:
        """Spawn a single creature at the specified position"""
        entity = self.ecs_world.create_entity()
        name = creature_data.get("name", "Unknown Creature")
        threat = creature_data.get("threat_level", "Medium")
        behavior = creature_data.get("behavior", "Neutral")
        loot = creature_data.get("loot", [])

        entity.add_component(PositionComponent(x, y))
        entity.add_component(CreatureComponent(name, threat, behavior, loot))
        logging.info(f"[WorldManager] Spawned creature '{name}' at ({x}, {y})")

    def distribute_biome_resources(self, biome_name: str, biome_info: dict, tiles: list) -> None:
        """Clustered deposit distribution per resource within a region"""
        richness = biome_info.get("richness", 1.0)
        resource_defs = biome_info.get("resources", [])
        rarity_ranges = {
            "Common": (10, 20),
            "Uncommon": (5, 10),
            "Rare": (2, 5),
            "Legendary": (1, 2)
        }
        # flatten tiles of this biome
        biome_tiles = [tile for row in tiles for tile in row if tile.get("biome") == biome_name]
        if not biome_tiles:
            return
        # create clustered deposits for each resource
        for res in resource_defs:
            name = res.get("name")
            rarity = res.get("rarity", "Common")
            if not name:
                continue
            base_min, base_max = rarity_ranges.get(rarity, (1, 3))
            # number of deposit clusters
            deposits = int(random.randint(base_min, base_max) * richness)
            for _ in range(deposits):
                tile = random.choice(biome_tiles)
                tile.setdefault("resources", {})
                # quantity per deposit
                qty = random.randint(1, max(1, int(richness)))
                entry = tile["resources"].setdefault(name, {"quantity": 0, "layer": res.get("type", "surface"), "rarity": rarity})
                entry["quantity"] += qty

    def setup_biome_events(self, biome_name: str, biome_info: dict) -> None:
        """Setup biome-specific events"""
        events = biome_info.get("special_events", [])
        for evt in events:
            event_name = evt.get("event_name", "Unnamed Event")
            trigger = evt.get("trigger", "on_day_10")
            self.event_manager.register_event(event_name, trigger)
            logging.info(f"[WorldManager] Set up event '{event_name}' for biome '{biome_name}'.")

    def get_current_season_and_year(self):
        """Get the current season, year, and season data based on the game time."""
        seasons_data = self.seasons_data if hasattr(self, 'seasons_data') else {
            "Spring": {"length": 30, "temperature_mod": 0},
            "Summer": {"length": 30, "temperature_mod": 10},
            "Fall": {"length": 30, "temperature_mod": 0},
            "Winter": {"length": 30, "temperature_mod": -10}
        }

        season_order = ["Spring", "Summer", "Fall", "Winter"]
        days_per_year = sum(seasons_data[s]["length"] for s in season_order)

        if not hasattr(self, 'ecs_world'):
            return "Spring", 1, seasons_data["Spring"], 1

        total_days = int(self.ecs_world.total_days)
        current_year = (total_days // days_per_year) + 1
        days_into_year = total_days % days_per_year

        # Find current season
        days_counted = 0
        current_season = season_order[0]
        for season in season_order:
            season_length = seasons_data[season]["length"]
            if days_into_year < days_counted + season_length:
                current_season = season
                break
            days_counted += season_length

        # Calculate day within the season
        day_in_season = days_into_year - days_counted + 1

        return current_season, current_year, seasons_data[current_season], day_in_season

    def get_current_weather(self):
        """Get the current weather based on season and random factors.
        Weather changes once per day rather than every tick.
        """
        if not hasattr(self, 'weather_data'):
            self.weather_data = {
                "Clear": {"weight": 10},
                "Cloudy": {"weight": 5},
                "Rain": {"weight": 3},
                "Storm": {"weight": 1}
            }

        # Initialize weather attributes if they don't exist
        if not hasattr(self, 'current_weather'):
            self.current_weather = "Clear"
            self.last_weather_day = -1  # Force initial weather generation
            self.weather_duration_days = 1  # Default to 1 day duration

        # Get current day to check if we need to update weather
        current_day = int(self.ecs_world.total_days) if hasattr(self, 'ecs_world') else 0
        current_season, _, _, _ = self.get_current_season_and_year()

        # Only update weather when the day changes and the duration has expired
        if current_day != self.last_weather_day and current_day % self.weather_duration_days == 0:
            logging.info(f"[Weather] Day {current_day}: Calculating new weather (previous: {self.current_weather})")

            # Adjust weights based on season
            season_mods = {
                "Spring": {"Rain": 2, "Storm": 1.5},
                "Summer": {"Clear": 1.5, "Storm": 2},
                "Fall": {"Cloudy": 1.5, "Rain": 1.2},
                "Winter": {"Clear": 0.7, "Cloudy": 1.5}
            }

            # Calculate new weather
            weights = []
            weather_types = []
            for weather, data in self.weather_data.items():
                base_weight = data["weight"]
                mod = season_mods.get(current_season, {}).get(weather, 1.0)
                weights.append(base_weight * mod)
                weather_types.append(weather)

            # Select new weather based on weighted probabilities
            self.current_weather = random.choices(weather_types, weights=weights)[0]

            # Weather lasts 1-3 days
            self.weather_duration_days = random.randint(1, 3)
            logging.info(f"[Weather] New weather: {self.current_weather} (will last for {self.weather_duration_days} days)")

            # Update the last weather day
            self.last_weather_day = current_day

        return self.current_weather
