import pytest
from core.ecs import <PERSON><PERSON><PERSON>, ECSWorld, System
from core.components import PositionComponent, CreatureComponent, CombatComponent

class CollisionSystem(System):
    """A simple collision detection system for testing."""

    def __init__(self):
        super().__init__()
        self.last_collisions = []

    def required_components(self):
        return [PositionComponent]

    def check_collision(self, entity1, entity2):
        """Check if two entities are colliding (occupying the same position)."""
        pos1 = entity1.get_component(PositionComponent)
        pos2 = entity2.get_component(PositionComponent)

        if not (pos1 and pos2):
            return False

        return pos1.x == pos2.x and pos1.y == pos2.y

    def get_entities_at_position(self, x, y):
        """Get all entities at a specific position."""
        result = []
        for entity in self.entities:
            pos = entity.get_component(PositionComponent)
            if pos.x == x and pos.y == y:
                result.append(entity)
        return result

    def update(self, dt, **kwargs):
        """Check for collisions between all entities."""
        collisions = []

        # Check each entity against all others
        for i, entity1 in enumerate(self.entities):
            for entity2 in self.entities[i+1:]:
                if self.check_collision(entity1, entity2):
                    collisions.append((entity1, entity2))

        # Store collisions for testing
        self.last_collisions = collisions
        return collisions


class TestCollisionDetection:
    """Test suite for collision detection."""

    @pytest.fixture
    def collision_world(self):
        """Create a world with a collision system."""
        world = ECSWorld()
        collision_system = CollisionSystem()
        world.add_system(collision_system)
        return world, collision_system

    def test_no_collision(self, collision_world):
        """Test that entities at different positions don't collide."""
        world, collision_system = collision_world

        # Create entities at different positions
        entity1 = world.create_entity()
        entity1.add_component(PositionComponent(1, 1))

        entity2 = world.create_entity()
        entity2.add_component(PositionComponent(2, 2))

        # Check for collisions
        collisions = collision_system.update(0.1)

        # There should be no collisions
        assert len(collisions) == 0

    def test_collision(self, collision_world):
        """Test that entities at the same position collide."""
        world, collision_system = collision_world

        # Create entities at the same position
        entity1 = world.create_entity()
        entity1.add_component(PositionComponent(1, 1))

        entity2 = world.create_entity()
        entity2.add_component(PositionComponent(1, 1))

        # Register entities with the system
        collision_system.register_entity(entity1)
        collision_system.register_entity(entity2)

        # Update the system
        collision_system.update(0.1)

        # There should be one collision
        assert len(collision_system.last_collisions) == 1
        assert (entity1, entity2) in collision_system.last_collisions or (entity2, entity1) in collision_system.last_collisions

    def test_multiple_collisions(self, collision_world):
        """Test detection of multiple collisions."""
        world, collision_system = collision_world

        # Create entities at various positions
        entity1 = world.create_entity()
        entity1.add_component(PositionComponent(1, 1))

        entity2 = world.create_entity()
        entity2.add_component(PositionComponent(1, 1))

        entity3 = world.create_entity()
        entity3.add_component(PositionComponent(1, 1))

        entity4 = world.create_entity()
        entity4.add_component(PositionComponent(2, 2))

        # Register entities with the system
        collision_system.register_entity(entity1)
        collision_system.register_entity(entity2)
        collision_system.register_entity(entity3)
        collision_system.register_entity(entity4)

        # Update the system
        collision_system.update(0.1)

        # There should be three collisions (entity1-entity2, entity1-entity3, entity2-entity3)
        assert len(collision_system.last_collisions) == 3

    def test_get_entities_at_position(self, collision_world):
        """Test retrieving all entities at a specific position."""
        world, collision_system = collision_world

        # Create entities at various positions
        entity1 = world.create_entity()
        entity1.add_component(PositionComponent(1, 1))

        entity2 = world.create_entity()
        entity2.add_component(PositionComponent(1, 1))

        entity3 = world.create_entity()
        entity3.add_component(PositionComponent(2, 2))

        # Register entities with the system
        collision_system.register_entity(entity1)
        collision_system.register_entity(entity2)
        collision_system.register_entity(entity3)

        # Get entities at position (1, 1)
        entities_at_1_1 = collision_system.get_entities_at_position(1, 1)

        # There should be two entities at (1, 1)
        assert len(entities_at_1_1) == 2
        assert entity1 in entities_at_1_1
        assert entity2 in entities_at_1_1

        # Get entities at position (2, 2)
        entities_at_2_2 = collision_system.get_entities_at_position(2, 2)

        # There should be one entity at (2, 2)
        assert len(entities_at_2_2) == 1
        assert entity3 in entities_at_2_2

        # Get entities at position (3, 3)
        entities_at_3_3 = collision_system.get_entities_at_position(3, 3)

        # There should be no entities at (3, 3)
        assert len(entities_at_3_3) == 0

    def test_combat_collision(self, collision_world):
        """Test collision detection for combat scenarios."""
        world, collision_system = collision_world

        # Create a player entity
        player = world.create_entity()
        player.add_component(PositionComponent(1, 1))
        player.add_component(CombatComponent(attack=10, defense=5))

        # Create an enemy entity adjacent to the player
        enemy = world.create_entity()
        enemy.add_component(PositionComponent(1, 2))
        enemy.add_component(CreatureComponent("Enemy", "hostile", 20, 20))
        combat_comp = CombatComponent(attack=8, defense=3)
        combat_comp.is_hostile = True
        enemy.add_component(combat_comp)

        # Register entities with the system
        collision_system.register_entity(player)
        collision_system.register_entity(enemy)

        # Check if they're adjacent (Manhattan distance = 1)
        player_pos = player.get_component(PositionComponent)
        enemy_pos = enemy.get_component(PositionComponent)

        manhattan_distance = abs(player_pos.x - enemy_pos.x) + abs(player_pos.y - enemy_pos.y)
        assert manhattan_distance == 1

        # Move the enemy to the player's position
        enemy_pos.x = player_pos.x
        enemy_pos.y = player_pos.y

        # Update the system
        collision_system.update(0.1)

        # There should be one collision
        assert len(collision_system.last_collisions) == 1
        assert (player, enemy) in collision_system.last_collisions or (enemy, player) in collision_system.last_collisions
