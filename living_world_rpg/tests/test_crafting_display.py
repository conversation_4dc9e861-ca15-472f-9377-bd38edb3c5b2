import pytest
import os
import sys
import pygame
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src')))

from core.ecs import Entity, ECSWorld
from core.components import InventoryComponent, PlayerComponent
from screens.crafting_screen import CraftingScreen

# Initialize pygame for testing
pygame.init()
pygame.display.set_mode((800, 600))

# Mock classes for testing
class MockGameState:
    def __init__(self):
        self.current_state = "crafting"
        self.feedback_messages = []
        self.message_duration = 2.0

    def show_feedback_message(self, message, color=(255,255,255)):
        self.feedback_messages.append((message, self.message_duration, self.message_duration, color))

@pytest.fixture
def setup_test_environment(monkeypatch):
    """Set up a test environment with a player entity, inventory, and crafting recipes."""
    # Create a world and player entity
    world = ECSWorld()
    player = world.create_entity()
    player.add_component(PlayerComponent("TestPlayer"))

    # Add inventory with some items
    inventory = InventoryComponent({
        "wood": 5,
        "stone": 3,
        "iron_ore": 2,
        "herbs": 4,
        "water": 2
    })
    player.add_component(inventory)

    # Make this the player entity in the world
    world.player_entity = player

    # Create test crafting recipes
    test_recipes = {
        "crafting_recipes": {
            "recipes": [
                {
                    "name": "Health Potion",
                    "description": "Restores health when consumed.",
                    "ingredients": {"herbs": 3, "water": 1},
                    "output": "healing_potion",
                    "output_qty": 1
                },
                {
                    "name": "Iron Sword",
                    "description": "A basic weapon.",
                    "ingredients": {"iron_ore": 5, "wood": 2},
                    "output": "iron_sword",
                    "output_qty": 1
                }
            ]
        }
    }

    # Create test items data
    test_items = {
        "herbs": {
            "type": "resource",
            "rarity": "common",
            "base_price": 8,
            "description": "A versatile plant used in potions and healing."
        },
        "iron_ore": {
            "type": "resource",
            "rarity": "common",
            "base_price": 15,
            "description": "Raw material for crafting weapons and armor."
        },
        "water": {
            "type": "resource",
            "rarity": "common",
            "base_price": 5,
            "description": "Essential for crafting potions."
        },
        "wood": {
            "type": "resource",
            "rarity": "common",
            "base_price": 10,
            "description": "Essential for building and crafting basic tools."
        },
        "stone": {
            "type": "resource",
            "rarity": "common",
            "base_price": 12,
            "description": "Used for crafting tools and buildings."
        },
        "healing_potion": {
            "type": "consumable",
            "rarity": "common",
            "base_price": 25,
            "description": "A potion that restores a small amount of health."
        },
        "iron_sword": {
            "type": "weapon",
            "rarity": "common",
            "base_price": 50,
            "description": "A basic iron sword for combat."
        }
    }

    # Create a mock main module
    class MockMain:
        data = {**test_recipes, "items": test_items}
        ecs_world = world

    # Patch the main module import in CraftingScreen.__init__
    import sys
    sys.modules['main'] = MockMain

    return world, player, MockMain

def test_crafting_screen_initialization(setup_test_environment, monkeypatch):
    """Test that the crafting screen initializes correctly."""
    world, player, mock_main = setup_test_environment

    # Create a game state
    gs = MockGameState()

    # Create the crafting screen
    screen = CraftingScreen(gs)

    # Check that recipes were loaded
    assert len(screen.recipes) == 2
    assert screen.recipes[0]["name"] == "Health Potion"
    assert screen.recipes[1]["name"] == "Iron Sword"

    # Check that items data was loaded
    assert "herbs" in screen.items_data
    assert "iron_ore" in screen.items_data

    # Check that default icon was created
    assert hasattr(screen, "default_icon")
    assert isinstance(screen.default_icon, pygame.Surface)

def test_crafting_screen_render(setup_test_environment, monkeypatch):
    """Test that the crafting screen renders without errors."""
    world, player, mock_main = setup_test_environment

    # Create a game state
    gs = MockGameState()

    # Create the crafting screen
    screen = CraftingScreen(gs)

    # Create a test surface
    test_surface = pygame.Surface((800, 600))

    # Render the screen (this should not raise any exceptions)
    screen.render(test_surface)

    # Basic assertions to ensure rendering happened
    assert screen.recipe_button_rects
    assert len(screen.recipe_button_rects) == 2
    assert screen.craft_button_rect is not None
    assert screen.back_button_rect is not None

def test_crafting_screen_try_craft(setup_test_environment, monkeypatch):
    """Test the try_craft method."""
    world, player, mock_main = setup_test_environment

    # Create a game state
    gs = MockGameState()

    # Create the crafting screen
    screen = CraftingScreen(gs)

    # Get the player's inventory
    inv_comp = player.get_component(InventoryComponent)

    # Try crafting a health potion (should succeed)
    health_potion_recipe = screen.recipes[0]
    assert screen.try_craft(health_potion_recipe)

    # Check that ingredients were consumed
    assert inv_comp.items["herbs"] == 1  # 4 - 3
    assert inv_comp.items["water"] == 1  # 2 - 1

    # Check that the potion was added
    assert inv_comp.items["healing_potion"] == 1

    # Try crafting an iron sword (should fail - not enough iron_ore)
    iron_sword_recipe = screen.recipes[1]
    assert not screen.try_craft(iron_sword_recipe)

    # Check that inventory remains unchanged for failed craft
    assert inv_comp.items["iron_ore"] == 2
    assert inv_comp.items["wood"] == 5
    assert "iron_sword" not in inv_comp.items

def test_crafting_screen_inventory_display(setup_test_environment, monkeypatch):
    """Test that the inventory display shows the correct items."""
    world, player, mock_main = setup_test_environment

    # Create a game state
    gs = MockGameState()

    # Create the crafting screen
    screen = CraftingScreen(gs)

    # Create a test surface
    test_surface = pygame.Surface((800, 600))

    # Render the screen
    screen.render(test_surface)

    # Add more items to inventory
    inv_comp = player.get_component(InventoryComponent)
    inv_comp.add_item("healing_potion", 2)

    # Render again
    screen.render(test_surface)

    # We can't easily test the visual output, but we can ensure no exceptions are raised
    # and that the code paths for inventory display are executed

    # Try crafting and check feedback messages
    health_potion_recipe = screen.recipes[0]
    screen.try_craft(health_potion_recipe)
    gs.show_feedback_message(f"Crafted {health_potion_recipe['name']}!", color=(0,255,0))

    assert len(gs.feedback_messages) == 1
    assert gs.feedback_messages[0][0] == "Crafted Health Potion!"
