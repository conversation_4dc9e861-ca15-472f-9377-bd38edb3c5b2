import pytest
import random
import string
from src.utils.elastic_hash_table import ElasticHashTable
from src.utils.elastic_dict_adapter import ElasticDictAdapter

def test_elastic_hash_table_basic():
    """Test basic operations of ElasticHashTable."""
    eht = ElasticHashTable(size=16)
    
    # Test insertion and retrieval
    eht.insert("key1", "value1")
    eht.insert("key2", "value2")
    
    assert eht.get("key1") == "value1"
    assert eht.get("key2") == "value2"
    assert eht.get("nonexistent") is None
    
    # Test removal
    eht.remove("key1")
    assert eht.get("key1") is None
    assert eht.get("key2") == "value2"
    
    # Test overwriting
    eht.insert("key2", "new_value")
    assert eht.get("key2") == "new_value"

def test_elastic_hash_table_resize():
    """Test that the hash table resizes correctly."""
    eht = ElasticHashTable(size=16)
    
    # Insert enough items to trigger resize
    for i in range(20):
        eht.insert(f"key_{i}", f"value_{i}")
    
    # Check that all items are still accessible
    for i in range(20):
        assert eht.get(f"key_{i}") == f"value_{i}"
    
    # Verify size has increased
    assert eht.size > 16

def test_elastic_hash_table_high_load():
    """Test performance with high load factor."""
    eht = ElasticHashTable(size=128)
    
    # Insert items to reach ~90% load factor
    for i in range(115):
        eht.insert(f"key_{i}", f"value_{i}")
    
    # Check that all items are accessible
    for i in range(115):
        assert eht.get(f"key_{i}") == f"value_{i}"

def test_elastic_dict_adapter_basic():
    """Test basic operations of ElasticDictAdapter."""
    eda = ElasticDictAdapter(size=16)
    
    # Test dictionary-style access
    eda["key1"] = "value1"
    eda["key2"] = "value2"
    
    assert eda["key1"] == "value1"
    assert eda["key2"] == "value2"
    
    with pytest.raises(KeyError):
        _ = eda["nonexistent"]
    
    # Test get with default
    assert eda.get("key1") == "value1"
    assert eda.get("nonexistent") is None
    assert eda.get("nonexistent", "default") == "default"
    
    # Test deletion
    del eda["key1"]
    assert "key1" not in eda
    assert "key2" in eda
    
    # Test length
    assert len(eda) == 1

def test_elastic_dict_adapter_methods():
    """Test additional methods of ElasticDictAdapter."""
    eda = ElasticDictAdapter(size=16)
    
    # Test update
    eda.update({"key1": "value1", "key2": "value2"})
    assert eda["key1"] == "value1"
    assert eda["key2"] == "value2"
    
    # Test items, keys, values
    items = list(eda.items())
    assert len(items) == 2
    assert ("key1", "value1") in items
    assert ("key2", "value2") in items
    
    keys = list(eda.keys())
    assert len(keys) == 2
    assert "key1" in keys
    assert "key2" in keys
    
    values = list(eda.values())
    assert len(values) == 2
    assert "value1" in values
    assert "value2" in values
    
    # Test pop
    assert eda.pop("key1") == "value1"
    assert "key1" not in eda
    
    # Test setdefault
    assert eda.setdefault("key3", "value3") == "value3"
    assert eda["key3"] == "value3"
    assert eda.setdefault("key3", "new_value") == "value3"  # Doesn't change
    
    # Test clear
    eda.clear()
    assert len(eda) == 0

def test_elastic_dict_adapter_initialization():
    """Test initialization of ElasticDictAdapter with existing data."""
    initial_data = {"key1": "value1", "key2": "value2", "key3": "value3"}
    eda = ElasticDictAdapter(initial_data, size=16)
    
    assert len(eda) == 3
    assert eda["key1"] == "value1"
    assert eda["key2"] == "value2"
    assert eda["key3"] == "value3"
    
    # Test copy
    eda_copy = eda.copy()
    assert len(eda_copy) == 3
    assert eda_copy["key1"] == "value1"
    
    # Test to_dict
    d = eda.to_dict()
    assert isinstance(d, dict)
    assert d == initial_data

def test_collision_handling():
    """Test handling of hash collisions."""
    # Create a class with controlled hash collisions
    class CollisionKey:
        def __init__(self, value, hash_value):
            self.value = value
            self.hash_value = hash_value
        
        def __hash__(self):
            return self.hash_value
        
        def __eq__(self, other):
            if isinstance(other, CollisionKey):
                return self.value == other.value
            return False
        
        def __repr__(self):
            return f"CollisionKey({self.value}, {self.hash_value})"
    
    eht = ElasticHashTable(size=16)
    
    # Create keys with the same hash but different values
    keys = [CollisionKey(i, 5) for i in range(10)]  # All have hash=5
    
    # Insert all keys
    for i, key in enumerate(keys):
        eht.insert(key, f"value_{i}")
    
    # Verify all keys are accessible
    for i, key in enumerate(keys):
        assert eht.get(key) == f"value_{i}"
    
    # Test removal with collisions
    eht.remove(keys[5])
    assert eht.get(keys[5]) is None
    
    # Make sure other keys with same hash are still accessible
    for i, key in enumerate(keys):
        if i != 5:
            assert eht.get(key) == f"value_{i}"
