"""
Test suite for the enhanced GameState system.
Tests the consolidated GameState with new features like state validation,
event notifications, persistence, and enhanced feedback messages.
"""

import pytest
import tempfile
import os
import time
import sys
from unittest.mock import Mock

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.game_state import GameState, GameStateEvent, FeedbackMessage


class TestEnhancedGameState:
    """Test the enhanced GameState functionality."""

    def setup_method(self):
        """Set up test fixtures."""
        self.gs = GameState()

    def test_initialization(self):
        """Test that GameState initializes with correct default values."""
        assert self.gs.current_state == "start_menu"
        assert self.gs.started_game is False
        assert self.gs.is_fullscreen is False
        assert self.gs.debug_overlay is False
        assert self.gs.is_paused is False
        assert self.gs.player_x == 0.0
        assert self.gs.player_y == 0.0
        assert self.gs.settlement_pos is None
        assert len(self.gs.feedback_messages) == 0

    def test_state_transition_validation(self):
        """Test state transition validation."""
        # Valid transition
        self.gs.current_state = "character_selection"
        assert self.gs.current_state == "character_selection"

        # Invalid transition (should be rejected)
        self.gs.current_state = "gameplay"  # Invalid from character_selection
        assert self.gs.current_state == "character_selection"  # Should remain unchanged

        # Quit should always be allowed
        self.gs.current_state = "quit"
        assert self.gs.current_state == "quit"

    def test_state_change_notifications(self):
        """Test state change event notifications."""
        callback_called = False
        callback_data = None

        def test_callback(data):
            nonlocal callback_called, callback_data
            callback_called = True
            callback_data = data

        # Register callback
        self.gs.register_callback(GameStateEvent.STATE_CHANGED, test_callback)

        # Change state
        self.gs.current_state = "character_selection"

        # Verify callback was called
        assert callback_called
        assert callback_data is not None
        assert callback_data['old_state'] == "start_menu"
        assert callback_data['new_state'] == "character_selection"

    def test_player_movement_notifications(self):
        """Test player movement event notifications."""
        callback_called = False
        callback_data = None

        def movement_callback(data):
            nonlocal callback_called, callback_data
            callback_called = True
            callback_data = data

        # Register callback
        self.gs.register_callback(GameStateEvent.PLAYER_MOVED, movement_callback)

        # Move player significantly
        self.gs.player_x = 5.0

        # Verify callback was called
        assert callback_called
        assert callback_data is not None
        assert callback_data['old_pos'] == (0.0, 0.0)
        assert callback_data['new_pos'] == (5.0, 0.0)

    def test_enhanced_feedback_messages(self):
        """Test enhanced feedback message system."""
        # Test basic message
        self.gs.show_feedback_message("Test message")
        assert len(self.gs.feedback_messages) == 1

        msg = self.gs.feedback_messages[0]
        assert isinstance(msg, FeedbackMessage)
        assert msg.text == "Test message"
        assert msg.priority == 0

        # Test priority message
        self.gs.show_error_message("Error message")
        assert len(self.gs.feedback_messages) == 2

        error_msg = self.gs.feedback_messages[1]
        assert error_msg.priority == 1
        assert error_msg.color == self.gs.error_message_color

    def test_message_priority_sorting(self):
        """Test that messages are sorted by priority."""
        # Add messages with different priorities
        self.gs.show_feedback_message("Low priority", priority=0)
        self.gs.show_error_message("High priority")  # priority=1
        self.gs.show_warning_message("Medium priority")  # priority=1

        active_messages = self.gs.get_active_messages()

        # Should be sorted by priority (highest first)
        assert active_messages[0].priority >= active_messages[1].priority
        assert active_messages[1].priority >= active_messages[2].priority

    def test_message_expiration(self):
        """Test message expiration functionality."""
        # Add message with short duration
        self.gs.show_feedback_message("Short message", duration=0.1)

        # Message should exist initially
        assert len(self.gs.get_active_messages()) == 1

        # Wait for expiration
        time.sleep(0.2)

        # Message should be expired and cleaned up
        assert len(self.gs.get_active_messages()) == 0

    def test_state_persistence(self):
        """Test state save and load functionality."""
        # Set up some state with valid transitions
        self.gs.current_state = "character_selection"
        self.gs.current_state = "biome_selection"
        self.gs.current_state = "seed_selection"
        self.gs.current_state = "loading"
        self.gs.current_state = "gameplay"
        self.gs.started_game = True
        self.gs.selected_seed = "test_seed"
        self.gs.player_race_name = "Human"
        self.gs.player_x = 10.5
        self.gs.player_y = 20.3
        self.gs.settlement_pos = (15, 25)

        # Save to temporary file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            temp_path = f.name

        try:
            # Save state
            success = self.gs.save_state(temp_path)
            assert success, "Failed to save state"
            assert os.path.exists(temp_path), "Save file was not created"

            # Create new GameState and load
            new_gs = GameState()
            load_success = new_gs.load_state(temp_path)
            assert load_success, "Failed to load state"

            # Verify state was restored
            assert new_gs.current_state == "gameplay", f"Expected 'gameplay', got '{new_gs.current_state}'"
            assert new_gs.started_game is True, "started_game not restored"
            assert new_gs.selected_seed == "test_seed", f"Expected 'test_seed', got '{new_gs.selected_seed}'"
            assert new_gs.player_race_name == "Human", f"Expected 'Human', got '{new_gs.player_race_name}'"
            assert new_gs.player_x == 10.5, f"Expected 10.5, got {new_gs.player_x}"
            assert new_gs.player_y == 20.3, f"Expected 20.3, got {new_gs.player_y}"
            assert new_gs.settlement_pos == (15, 25), f"Expected (15, 25), got {new_gs.settlement_pos}"

        except Exception as e:
            print(f"Debug info - temp_path: {temp_path}")
            if os.path.exists(temp_path):
                with open(temp_path, 'r') as f:
                    print(f"File contents: {f.read()}")
            raise e
        finally:
            # Clean up
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_state_history_tracking(self):
        """Test state history tracking."""
        # Change states
        self.gs.current_state = "character_selection"
        self.gs.current_state = "biome_selection"
        self.gs.current_state = "seed_selection"

        # Check history
        history = self.gs.get_state_history()
        assert len(history) == 3
        assert history[0][0] == "character_selection"
        assert history[1][0] == "biome_selection"
        assert history[2][0] == "seed_selection"

    def test_state_info_debugging(self):
        """Test state information for debugging."""
        # Add some state
        self.gs.show_feedback_message("Test message")
        self.gs.current_state = "character_selection"

        info = self.gs.get_state_info()

        assert info['current_state'] == "character_selection"
        assert info['previous_state'] == "start_menu"
        assert info['state_change_count'] == 1
        assert info['active_messages'] == 1
        assert info['player_position'] == (0.0, 0.0)
        assert info['started_game'] is False

    def test_validation_toggle(self):
        """Test enabling/disabling state validation."""
        # Disable validation
        self.gs.enable_validation(False)

        # Should allow invalid transition
        self.gs.current_state = "gameplay"  # Invalid from start_menu
        assert self.gs.current_state == "gameplay"

        # Re-enable validation
        self.gs.enable_validation(True)

        # Should reject invalid transition (gameplay can only go to pause, inventory, crafting, or start_menu)
        self.gs.current_state = "character_selection"  # Invalid from gameplay
        assert self.gs.current_state == "gameplay"  # Should remain unchanged

    def test_callback_management(self):
        """Test callback registration and unregistration."""
        callback1 = Mock()
        callback2 = Mock()

        # Register callbacks
        self.gs.register_callback(GameStateEvent.STATE_CHANGED, callback1)
        self.gs.register_callback(GameStateEvent.STATE_CHANGED, callback2)

        # Trigger event
        self.gs.current_state = "character_selection"

        # Both callbacks should be called
        assert callback1.called
        assert callback2.called

        # Unregister one callback
        self.gs.unregister_callback(GameStateEvent.STATE_CHANGED, callback1)

        # Reset mocks
        callback1.reset_mock()
        callback2.reset_mock()

        # Trigger another event
        self.gs.current_state = "biome_selection"

        # Only callback2 should be called
        assert not callback1.called
        assert callback2.called

    def test_state_reset(self):
        """Test state reset functionality."""
        # Modify state
        self.gs.current_state = "gameplay"
        self.gs.started_game = True
        self.gs.player_x = 100.0
        self.gs.show_feedback_message("Test message")

        # Reset state
        self.gs.reset_state()

        # Verify reset to defaults
        assert self.gs.current_state == "start_menu"
        assert self.gs.started_game is False
        assert self.gs.player_x == 0.0
        assert len(self.gs.feedback_messages) == 0

    def test_message_cleanup(self):
        """Test automatic message cleanup."""
        # Add expired message manually
        expired_msg = FeedbackMessage(
            text="Expired",
            timestamp=time.time() - 10,  # 10 seconds ago
            duration=1.0,  # 1 second duration
            color=(255, 255, 255)
        )
        self.gs.feedback_messages.append(expired_msg)

        # Add current message
        self.gs.show_feedback_message("Current message")

        # Get active messages (should trigger cleanup)
        active = self.gs.get_active_messages()

        # Only current message should remain
        assert len(active) == 1
        assert active[0].text == "Current message"


if __name__ == "__main__":
    # Run tests manually
    test_instance = TestEnhancedGameState()

    # Run all test methods
    test_methods = [method for method in dir(test_instance) if method.startswith('test_')]

    print(f"Running {len(test_methods)} tests for Enhanced GameState...")

    passed = 0
    failed = 0

    for test_method in test_methods:
        try:
            print(f"Running {test_method}...", end=" ")
            test_instance.setup_method()  # Reset state
            getattr(test_instance, test_method)()
            print("PASSED")
            passed += 1
        except Exception as e:
            print(f"FAILED: {e}")
            failed += 1

    print(f"\nResults: {passed} passed, {failed} failed")

    if failed == 0:
        print("✅ All tests passed! Enhanced GameState is working correctly.")
    else:
        print("❌ Some tests failed. Check the implementation.")
