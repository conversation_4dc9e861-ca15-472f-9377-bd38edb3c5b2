import pytest
import uuid
import sys
import os

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'src'))

# Try to import with different path formats to handle various run contexts
try:
    # First try direct imports
    try:
        from core.ecs import Entity, ECSWorld
        from core.components import (
            PlayerComponent, PositionComponent, InventoryComponent,
            StatsComponent, CombatComponent, ToolComponent, GatheringSkillComponent,
            AIComponent, CreatureComponent
        )
        print("Successfully imported game modules with direct imports")
    except ImportError:
        # Then try with 'src.' prefix
        from src.core.ecs import Entity, ECSWorld
        from src.core.components import (
            PlayerComponent, PositionComponent, InventoryComponent,
            Stats<PERSON>omponent, <PERSON><PERSON><PERSON>ponent, Too<PERSON><PERSON>omponent, GatheringS<PERSON>Component,
            AIComponent, CreatureComponent
        )
        print("Successfully imported game modules with 'src.' prefix")
except ImportError as e:
    print(f"Failed to import game modules: {e}")
    sys.exit(1)

class TestEntityCreation:
    """Test suite for entity creation and component management."""

    def test_entity_initialization(self):
        """Test that entities are properly initialized with unique IDs."""
        entity1 = Entity()
        entity2 = Entity()

        # Entities should have unique IDs
        assert entity1.id != entity2.id
        # ID should be a UUID
        assert isinstance(entity1.id, uuid.UUID)
        # Components dictionary should be initialized
        assert hasattr(entity1, 'components')
        assert len(entity1.components) == 0

    def test_add_component(self):
        """Test adding components to an entity."""
        entity = Entity()

        # Add a position component
        pos_comp = PositionComponent(10, 20)
        entity.add_component(pos_comp)

        # Component should be stored by its type
        assert entity.get_component(PositionComponent) is pos_comp
        assert len(entity.components) == 1

        # Add another component
        inv_comp = InventoryComponent()
        entity.add_component(inv_comp)

        # Both components should be accessible
        assert entity.get_component(PositionComponent) is pos_comp
        assert entity.get_component(InventoryComponent) is inv_comp
        assert len(entity.components) == 2

    def test_remove_component(self):
        """Test removing components from an entity."""
        entity = Entity()

        # Add components
        pos_comp = PositionComponent(10, 20)
        inv_comp = InventoryComponent()
        entity.add_component(pos_comp)
        entity.add_component(inv_comp)

        # Remove a component
        entity.remove_component(PositionComponent)

        # Component should be gone
        assert entity.get_component(PositionComponent) is None
        assert entity.get_component(InventoryComponent) is inv_comp
        assert len(entity.components) == 1

        # Removing a non-existent component should not raise an error
        entity.remove_component(StatsComponent)
        assert len(entity.components) == 1

    def test_world_entity_creation(self):
        """Test entity creation through the ECSWorld."""
        world = ECSWorld()

        # Create an entity through the world
        entity = world.create_entity()

        # Entity should be registered with the world
        assert entity in world.entities
        assert len(world.entities) == 1

    def test_player_entity_creation(self):
        """Test creating a player entity with all required components."""
        world = ECSWorld()

        # Create a player entity
        player = world.create_entity()
        player.add_component(PlayerComponent("TestHero"))
        player.add_component(PositionComponent(5, 5))
        player.add_component(StatsComponent())
        player.add_component(CombatComponent(attack=10, defense=5))
        player.add_component(InventoryComponent())

        # Test that the player entity has all required components
        assert player.get_component(PlayerComponent) is not None
        assert player.get_component(PlayerComponent).name == "TestHero"
        assert player.get_component(PositionComponent) is not None
        assert player.get_component(PositionComponent).x == 5
        assert player.get_component(PositionComponent).y == 5
        assert player.get_component(StatsComponent) is not None
        assert player.get_component(CombatComponent) is not None
        assert player.get_component(CombatComponent).attack == 10
        assert player.get_component(CombatComponent).defense == 5
        assert player.get_component(InventoryComponent) is not None

        # Test get_player_entity method
        assert world.get_player_entity() is player

    def test_npc_entity_creation(self):
        """Test creating an NPC entity with AI component."""
        world = ECSWorld()

        # Create an NPC entity
        # AIComponent and CreatureComponent should already be imported from the top-level imports

        npc = world.create_entity()
        npc.add_component(PositionComponent(10, 10))
        npc.add_component(CreatureComponent("TestNPC", "humanoid", 20, 20))
        npc.add_component(InventoryComponent())
        npc.add_component(StatsComponent())
        npc.add_component(AIComponent("behavior_tree"))

        # Test that the NPC entity has all required components
        assert npc.get_component(PositionComponent) is not None
        assert npc.get_component(PositionComponent).x == 10
        assert npc.get_component(PositionComponent).y == 10
        assert npc.get_component(CreatureComponent) is not None
        assert npc.get_component(CreatureComponent).creature_name == "TestNPC"
        assert npc.get_component(InventoryComponent) is not None
        assert npc.get_component(StatsComponent) is not None
        assert npc.get_component(AIComponent) is not None
        assert npc.get_component(AIComponent).behavior_type == "behavior_tree"

    def test_get_entities_with(self):
        """Test filtering entities by component types."""
        world = ECSWorld()

        # Create entities with different component combinations
        entity1 = world.create_entity()
        entity1.add_component(PositionComponent(1, 1))
        entity1.add_component(InventoryComponent())

        entity2 = world.create_entity()
        entity2.add_component(PositionComponent(2, 2))
        entity2.add_component(StatsComponent())

        entity3 = world.create_entity()
        entity3.add_component(PositionComponent(3, 3))
        entity3.add_component(InventoryComponent())
        entity3.add_component(StatsComponent())

        # Test filtering by a single component type
        entities_with_pos = world.get_entities_with(PositionComponent)
        assert len(entities_with_pos) == 3

        entities_with_inv = world.get_entities_with(InventoryComponent)
        assert len(entities_with_inv) == 2

        entities_with_stats = world.get_entities_with(StatsComponent)
        assert len(entities_with_stats) == 2

        # Test filtering by multiple component types
        entities_with_pos_inv = world.get_entities_with(PositionComponent, InventoryComponent)
        assert len(entities_with_pos_inv) == 2

        entities_with_pos_stats = world.get_entities_with(PositionComponent, StatsComponent)
        assert len(entities_with_pos_stats) == 2

        entities_with_all = world.get_entities_with(PositionComponent, InventoryComponent, StatsComponent)
        assert len(entities_with_all) == 1
        assert entities_with_all[0][0] is entity3
