import pytest
import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src')))

from core.ecs import Entity, ECSWorld
from core.components import InventoryComponent, PlayerComponent, PositionComponent
from screens.crafting_screen import CraftingScreen

# Mock classes for testing
class MockGameState:
    def __init__(self):
        self.current_state = "crafting"
        self.feedback_messages = []
        self.message_duration = 2.0

    def show_feedback_message(self, message, color=(255,255,255)):
        self.feedback_messages.append((message, self.message_duration, self.message_duration, color))

@pytest.fixture
def setup_inventory():
    """Set up a test environment with a player entity and inventory."""
    # Create a world and player entity
    world = ECSWorld()
    player = world.create_entity()
    player.add_component(PlayerComponent("TestPlayer"))
    player.add_component(PositionComponent(5, 5))

    # Add inventory with some items
    inventory = InventoryComponent({
        "wood": 5,
        "stone": 3,
        "iron_ore": 2,
        "leather": 1
    })
    player.add_component(inventory)

    # Make this the player entity in the world
    world.player_entity = player

    # Return the world and player for testing
    return world, player

@pytest.fixture
def setup_crafting_recipes():
    """Set up test crafting recipes."""
    return {
        "crafting_recipes": {
            "recipes": [
                {
                    "name": "Wood Plank",
                    "description": "Basic building material.",
                    "ingredients": {"wood": 2},
                    "output": "wood_plank",
                    "output_qty": 4
                },
                {
                    "name": "Stone Axe",
                    "description": "Basic tool for chopping wood.",
                    "ingredients": {"wood": 2, "stone": 3},
                    "output": "stone_axe",
                    "output_qty": 1
                },
                {
                    "name": "Iron Sword",
                    "description": "Basic weapon.",
                    "ingredients": {"wood": 1, "iron_ore": 3},
                    "output": "iron_sword",
                    "output_qty": 1
                }
            ]
        }
    }

def test_inventory_add_item(setup_inventory):
    """Test adding items to inventory."""
    _, player = setup_inventory
    inv_comp = player.get_component(InventoryComponent)

    # Add a new item
    inv_comp.add_item("gold", 5)
    assert inv_comp.items["gold"] == 5

    # Add more of an existing item
    inv_comp.add_item("wood", 3)
    assert inv_comp.items["wood"] == 8

    # Add zero of an item (should not add)
    inv_comp.add_item("diamond", 0)
    assert "diamond" not in inv_comp.items

def test_inventory_remove_item(setup_inventory):
    """Test removing items from inventory."""
    _, player = setup_inventory
    inv_comp = player.get_component(InventoryComponent)

    # Remove some of an item
    inv_comp.remove_item("wood", 2)
    assert inv_comp.items["wood"] == 3

    # Remove all of an item
    inv_comp.remove_item("leather", 1)
    assert "leather" not in inv_comp.items

    # Try to remove more than available
    inv_comp.remove_item("stone", 5)
    assert "stone" not in inv_comp.items  # Should remove all

    # Try to remove non-existent item
    inv_comp.remove_item("diamond", 1)
    assert "diamond" not in inv_comp.items

def test_inventory_has_items(setup_inventory):
    """Test checking if inventory has items."""
    _, player = setup_inventory
    inv_comp = player.get_component(InventoryComponent)

    # Check for existing items
    assert inv_comp.has_items({"wood": 3})
    assert inv_comp.has_items({"wood": 2, "stone": 2})

    # Check for too many items
    assert not inv_comp.has_items({"wood": 10})
    assert not inv_comp.has_items({"wood": 2, "stone": 5})

    # Check for non-existent items
    assert not inv_comp.has_items({"diamond": 1})
    assert not inv_comp.has_items({"wood": 2, "diamond": 1})

def test_crafting_success(setup_inventory, setup_crafting_recipes, monkeypatch):
    """Test successful crafting."""
    world, player = setup_inventory

    # Mock the main module and data
    class MockMain:
        data = setup_crafting_recipes
        ecs_world = world

    # Create a crafting screen with our mocks
    gs = MockGameState()

    # Create the crafting screen
    screen = CraftingScreen(gs)

    # Replace the data and ecs_world attributes
    screen.data = setup_crafting_recipes

    # Monkeypatch the try_craft method to use our world
    original_try_craft = screen.try_craft

    def patched_try_craft(recipe):
        # Check required ingredients
        inv_comp = player.get_component(InventoryComponent)
        if not inv_comp:
            return False

        for name, count in recipe.get("ingredients", {}).items():
            if inv_comp.items.get(name, 0) < count:
                return False

        # Deduct ingredients
        for name, count in recipe.get("ingredients", {}).items():
            inv_comp.items[name] -= count
            if inv_comp.items[name] <= 0:
                del inv_comp.items[name]

        # Add crafted item(s)
        out = recipe.get("output")
        qty = recipe.get("output_qty", 1)
        inv_comp.items[out] = inv_comp.items.get(out, 0) + qty

        return True

    monkeypatch.setattr(screen, "try_craft", patched_try_craft)

    # Set up recipes
    screen.recipes = setup_crafting_recipes["crafting_recipes"]["recipes"]

    # Get the wood plank recipe
    wood_plank_recipe = screen.recipes[0]

    # Try crafting
    result = screen.try_craft(wood_plank_recipe)
    assert result is True

    # Check inventory changes
    inv_comp = player.get_component(InventoryComponent)
    assert inv_comp.items["wood"] == 3  # 5 - 2
    assert inv_comp.items["wood_plank"] == 4  # Based on the recipe output_qty

def test_crafting_failure_insufficient_materials(setup_inventory, setup_crafting_recipes, monkeypatch):
    """Test crafting failure due to insufficient materials."""
    world, player = setup_inventory

    # Create a crafting screen with our mocks
    gs = MockGameState()

    # Create the crafting screen
    screen = CraftingScreen(gs)

    # Replace the data and ecs_world attributes
    screen.data = setup_crafting_recipes

    # Monkeypatch the try_craft method to use our world
    original_try_craft = screen.try_craft

    def patched_try_craft(recipe):
        # Check required ingredients
        inv_comp = player.get_component(InventoryComponent)
        if not inv_comp:
            return False

        for name, count in recipe.get("ingredients", {}).items():
            if inv_comp.items.get(name, 0) < count:
                return False

        # Deduct ingredients
        for name, count in recipe.get("ingredients", {}).items():
            inv_comp.items[name] -= count
            if inv_comp.items[name] <= 0:
                del inv_comp.items[name]

        # Add crafted item(s)
        out = recipe.get("output")
        qty = recipe.get("output_qty", 1)
        inv_comp.items[out] = inv_comp.items.get(out, 0) + qty

        return True

    monkeypatch.setattr(screen, "try_craft", patched_try_craft)

    # Set up recipes
    screen.recipes = setup_crafting_recipes["crafting_recipes"]["recipes"]

    # Get the iron sword recipe (requires 3 iron_ore, but we only have 2)
    iron_sword_recipe = screen.recipes[2]

    # Try crafting
    result = screen.try_craft(iron_sword_recipe)
    assert result is False

    # Check inventory remains unchanged
    inv_comp = player.get_component(InventoryComponent)
    assert inv_comp.items["wood"] == 5
    assert inv_comp.items["iron_ore"] == 2
    assert "iron_sword" not in inv_comp.items

def test_crafting_feedback_messages(setup_inventory, setup_crafting_recipes, monkeypatch):
    """Test feedback messages during crafting."""
    world, player = setup_inventory

    # Create a crafting screen with our mocks
    gs = MockGameState()

    # Create the crafting screen
    screen = CraftingScreen(gs)

    # Set up recipes
    screen.data = setup_crafting_recipes
    screen.recipes = setup_crafting_recipes["crafting_recipes"]["recipes"]

    # Get recipes
    wood_plank_recipe = screen.recipes[0]
    iron_sword_recipe = screen.recipes[2]

    # Simulate crafting success
    gs.show_feedback_message(f"Crafted {wood_plank_recipe['name']}!", color=(0,255,0))

    # Simulate crafting failure
    gs.show_feedback_message("Missing ingredients!", color=(255,0,0))

    # Check feedback messages
    assert len(gs.feedback_messages) == 2
    assert gs.feedback_messages[0][0] == "Crafted Wood Plank!"
    assert gs.feedback_messages[0][3] == (0,255,0)
    assert gs.feedback_messages[1][0] == "Missing ingredients!"
    assert gs.feedback_messages[1][3] == (255,0,0)

def test_inventory_component_methods(setup_inventory):
    """Test additional InventoryComponent methods."""
    _, player = setup_inventory
    inv_comp = player.get_component(InventoryComponent)

    # Test get_item_count
    assert inv_comp.get_item_count("wood") == 5
    assert inv_comp.get_item_count("non_existent") == 0

    # Test clear method
    inv_comp.clear()
    assert len(inv_comp.items) == 0

    # Test dictionary-like behavior
    inv_comp.items = {"wood": 5, "stone": 3}
    assert "wood" in inv_comp.items
    assert inv_comp.items["wood"] == 5
    assert inv_comp.items["stone"] == 3
