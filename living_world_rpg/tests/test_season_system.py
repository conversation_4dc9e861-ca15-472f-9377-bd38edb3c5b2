import pytest
import json
import os
from unittest.mock import patch, MagicMock

# Import our mock classes from the weather system tests
from tests.test_weather_system import (
    MockECSWorld, <PERSON>ckEntity, MockSettlementComponent,
    MockInventoryComponent, MockResourceProductionSystem, MockWorldManager
)

class TestSeasonSystem:
    """Test suite for the season system."""

    @pytest.fixture
    def seasons_data(self):
        """Load seasons data from the JSON file."""
        data_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'seasons.json')
        with open(data_path, 'r') as f:
            return json.load(f)

    @pytest.fixture
    def world_manager(self):
        """Create a MockWorldManager instance."""
        return MockWorldManager()

    def test_seasons_data_loading(self, seasons_data):
        """Test that seasons data is loaded correctly."""
        # Check that the seasons data contains expected seasons
        assert "Spring" in seasons_data
        assert "Summer" in seasons_data
        assert "Autumn" in seasons_data or "Fall" in seasons_data
        assert "Winter" in seasons_data

        # Check that each season has a modifier
        for season, data in seasons_data.items():
            assert "modifier" in data
            assert isinstance(data["modifier"], (int, float))

    def test_get_current_season(self, world_manager):
        """Test that get_current_season_and_year returns the correct season based on game time."""
        # Set the game time to day 0 (should be Spring)
        world_manager.ecs_world.day_time = 12.0  # Noon
        world_manager.ecs_world.game_days = 0

        season, year, season_data, day = world_manager.get_current_season_and_year()
        assert season == "Spring"
        assert year == 1
        assert day == 1

        # Set the game time to day 35 (should be Summer)
        world_manager.ecs_world.game_days = 35

        season, year, season_data, day = world_manager.get_current_season_and_year()
        assert season == "Summer"
        assert year == 1
        assert day == 6  # 35 - 30 (Spring length) + 1

        # Set the game time to day 65 (should be Fall)
        world_manager.ecs_world.game_days = 65

        season, year, season_data, day = world_manager.get_current_season_and_year()
        assert season == "Fall"
        assert year == 1
        assert day == 6  # 65 - 30 (Spring) - 30 (Summer) + 1

        # Set the game time to day 95 (should be Winter)
        world_manager.ecs_world.game_days = 95

        season, year, season_data, day = world_manager.get_current_season_and_year()
        assert season == "Winter"
        assert year == 1
        assert day == 6  # 95 - 30 (Spring) - 30 (Summer) - 30 (Fall) + 1

        # Set the game time to day 125 (should be Spring of year 2)
        world_manager.ecs_world.game_days = 125

        season, year, season_data, day = world_manager.get_current_season_and_year()
        assert season == "Spring"
        assert year == 2
        assert day == 6  # 125 - 120 (year 1) + 1

    def test_season_effect_on_resources(self):
        """Test that seasons affect resource production."""
        # Create a world and system
        world = MockECSWorld()

        # Create weather data
        weather_data = {
            "Clear": {
                "resource_modifiers": {
                    "grain": 1.0,
                    "wood": 1.0
                }
            }
        }

        # Create seasonal data
        seasonal_data = {
            "Spring": {
                "modifier": 1.2
            },
            "Summer": {
                "modifier": 1.0
            },
            "Fall": {
                "modifier": 0.8
            },
            "Winter": {
                "modifier": 0.5
            }
        }

        # Create a resource production system
        system = MockResourceProductionSystem(weather_data, seasonal_data)
        world.add_system(system)

        # Create a settlement entity
        entity = world.create_entity()
        settlement = MockSettlementComponent("Test Settlement", "Forest", "Faction",
                                         {"grain": 10, "wood": 10}, {})
        inventory = MockInventoryComponent({})
        entity.add_component(settlement)
        entity.add_component(inventory)
        system.entities.append(entity)

        # Update in Spring
        system.update(1.0, current_season="Spring", current_weather="Clear")

        # Check resource production in Spring
        assert inventory.items["grain"] == 12  # 10 * 1.2 * 1.0
        assert inventory.items["wood"] == 12   # 10 * 1.2 * 1.0

        # Reset inventory
        inventory.items = {}

        # Update in Summer
        system.update(1.0, current_season="Summer", current_weather="Clear")

        # Check resource production in Summer
        assert inventory.items["grain"] == 10  # 10 * 1.0 * 1.0
        assert inventory.items["wood"] == 10   # 10 * 1.0 * 1.0

        # Reset inventory
        inventory.items = {}

        # Update in Fall
        system.update(1.0, current_season="Fall", current_weather="Clear")

        # Check resource production in Fall
        assert inventory.items["grain"] == 8  # 10 * 0.8 * 1.0
        assert inventory.items["wood"] == 8   # 10 * 0.8 * 1.0

        # Reset inventory
        inventory.items = {}

        # Update in Winter
        system.update(1.0, current_season="Winter", current_weather="Clear")

        # Check resource production in Winter
        assert inventory.items["grain"] == 5  # 10 * 0.5 * 1.0
        assert inventory.items["wood"] == 5   # 10 * 0.5 * 1.0

    def test_season_year_calculation(self, world_manager):
        """Test that season and year are calculated correctly for different game times."""
        # Test various game times
        test_cases = [
            # (game_days, expected_season, expected_year, expected_day)
            (0, "Spring", 1, 1),
            (29, "Spring", 1, 30),
            (30, "Summer", 1, 1),
            (59, "Summer", 1, 30),
            (60, "Fall", 1, 1),
            (89, "Fall", 1, 30),
            (90, "Winter", 1, 1),
            (119, "Winter", 1, 30),
            (120, "Spring", 2, 1),
            (240, "Spring", 3, 1),
            (360, "Spring", 4, 1)
        ]

        for game_days, expected_season, expected_year, expected_day in test_cases:
            world_manager.ecs_world.game_days = game_days
            season, year, _, day = world_manager.get_current_season_and_year()

            assert season == expected_season, f"Failed for game_days={game_days}"
            assert year == expected_year, f"Failed for game_days={game_days}"
            assert day == expected_day, f"Failed for game_days={game_days}"

    def test_fractional_days(self, world_manager):
        """Test that fractional days are handled correctly."""
        # Set the game time to day 29.5 (should still be Spring)
        world_manager.ecs_world.game_days = 29
        world_manager.ecs_world.day_time = 12.0  # Noon (0.5 day)

        season, year, _, day = world_manager.get_current_season_and_year()
        assert season == "Spring"
        assert year == 1
        assert day == 30  # 29 + 1

        # Set the game time to day 29.9 (should still be Spring)
        world_manager.ecs_world.game_days = 29
        world_manager.ecs_world.day_time = 21.6  # 90% through the day

        season, year, _, day = world_manager.get_current_season_and_year()
        assert season == "Spring"
        assert year == 1
        assert day == 30  # 29 + 1

        # Set the game time to day 30.0 (should be Summer)
        world_manager.ecs_world.game_days = 30
        world_manager.ecs_world.day_time = 0.0  # Start of day

        season, year, _, day = world_manager.get_current_season_and_year()
        assert season == "Summer"
        assert year == 1
        assert day == 1  # First day of Summer
