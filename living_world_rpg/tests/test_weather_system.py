import pytest
import json
import os
import random
from unittest.mock import patch, MagicMock, Mock

# Create mock classes to avoid dependencies
class MockECSWorld:
    def __init__(self):
        self.day_time = 12.0
        self.game_days = 0
        self.total_days = 0  # Add total_days attribute for weather system
        self.entities = []
        self.systems = []

    def create_entity(self):
        entity = MockEntity()
        self.entities.append(entity)
        return entity

    def add_system(self, system):
        self.systems.append(system)
        system.world = self

class MockEntity:
    def __init__(self):
        self.components = {}

    def add_component(self, component):
        self.components[type(component)] = component

    def get_component(self, component_type):
        return self.components.get(component_type)

class MockSettlementComponent:
    def __init__(self, name, biome, faction, production_rate, consumption_rate):
        self.name = name
        self.biome = biome
        self.faction = faction
        self.production_rate = production_rate
        self.consumption_rate = consumption_rate

class MockInventoryComponent:
    def __init__(self, initial_items=None):
        self.items = initial_items or {}

class MockResourceProductionSystem:
    def __init__(self, weather_data, seasonal_data):
        self.weather_data = weather_data
        self.seasonal_data = seasonal_data
        self.entities = []

    def update(self, dt, current_season="Spring", current_weather="Clear", **kwargs):
        season_factor = self.seasonal_data.get(current_season, {}).get("modifier", 1.0)
        weather_mods = self.weather_data.get(current_weather, {}).get("resource_modifiers", {})

        for entity in self.entities:
            settlement = entity.get_component(MockSettlementComponent)
            inventory = entity.get_component(MockInventoryComponent)
            if not settlement or not inventory:
                continue
            for resource, base_amt in settlement.production_rate.items():
                w_mod = weather_mods.get(resource.lower(), 1.0)
                final_amt = int(base_amt * season_factor * w_mod)
                inventory.items[resource] = inventory.items.get(resource, 0) + final_amt

class MockWorldManager:
    def __init__(self, seasons_data=None, weather_data=None):
        self.ecs_world = MockECSWorld()
        self.seasons_data = seasons_data or {
            "Spring": {"length": 30, "modifier": 1.2},
            "Summer": {"length": 30, "modifier": 1.0},
            "Fall": {"length": 30, "modifier": 0.8},
            "Winter": {"length": 30, "modifier": 0.5}
        }
        self.weather_data = weather_data or {
            "Clear": {"weight": 10},
            "Cloudy": {"weight": 5},
            "Rain": {"weight": 3},
            "Storm": {"weight": 1}
        }
        self.current_weather = "Clear"
        self.weather_duration = 0

    def get_current_season_and_year(self):
        seasons_order = ["Spring", "Summer", "Fall", "Winter"]
        days_per_year = sum(self.seasons_data[s]["length"] for s in seasons_order)

        total_days = self.ecs_world.game_days
        current_year = (total_days // days_per_year) + 1
        days_into_year = total_days % days_per_year

        days_counted = 0
        current_season = seasons_order[0]
        for season in seasons_order:
            season_length = self.seasons_data[season]["length"]
            if days_into_year < days_counted + season_length:
                current_season = season
                break
            days_counted += season_length

        day_in_season = days_into_year - days_counted + 1

        return current_season, current_year, self.seasons_data[current_season], day_in_season

    def get_current_weather(self):
        # Initialize weather attributes if they don't exist
        if not hasattr(self, 'last_weather_day'):
            self.last_weather_day = -1  # Force initial weather generation
            self.weather_duration_days = 1  # Default to 1 day duration

        # Get current day to check if we need to update weather
        current_day = int(self.ecs_world.total_days) if hasattr(self, 'ecs_world') else 0
        current_season, _, _, _ = self.get_current_season_and_year()

        # Only update weather when the day changes and the duration has expired
        if current_day != self.last_weather_day and current_day % self.weather_duration_days == 0:
            # Calculate season-adjusted weights
            season_mods = {
                "Spring": {"Rain": 2, "Storm": 1.5},
                "Summer": {"Clear": 1.5, "Storm": 2},
                "Fall": {"Cloudy": 1.5, "Rain": 1.2},
                "Winter": {"Clear": 0.7, "Cloudy": 1.5}
            }

            weights = []
            weather_types = []
            for weather, data in self.weather_data.items():
                base_weight = data["weight"]
                mod = season_mods.get(current_season, {}).get(weather, 1.0)
                weights.append(base_weight * mod)
                weather_types.append(weather)

            self.current_weather = random.choices(weather_types, weights=weights)[0]
            self.weather_duration_days = random.randint(1, 3)  # Weather lasts 1-3 days
            self.last_weather_day = current_day

        return self.current_weather

class TestWeatherSystem:
    """Test suite for the weather system."""

    @pytest.fixture
    def weather_data(self):
        """Load weather data from the JSON file."""
        data_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'weather.json')
        with open(data_path, 'r') as f:
            return json.load(f)

    @pytest.fixture
    def world_manager(self):
        """Create a MockWorldManager instance."""
        return MockWorldManager()

    def test_weather_data_loading(self, weather_data):
        """Test that weather data is loaded correctly."""
        # Check that the weather data contains expected weather types
        assert "Clear" in weather_data
        assert "Rain" in weather_data
        assert "Storm" in weather_data

        # Check that each weather type has resource modifiers
        for weather_type, data in weather_data.items():
            assert "resource_modifiers" in data
            assert isinstance(data["resource_modifiers"], dict)

    def test_get_current_weather(self, world_manager):
        """Test that get_current_weather returns a valid weather type."""
        # Get the current weather
        weather = world_manager.get_current_weather()

        # Check that it's a valid weather type
        assert weather in ["Clear", "Cloudy", "Rain", "Storm"]

        # Check that weather tracking attributes are set
        assert hasattr(world_manager, 'last_weather_day')
        assert hasattr(world_manager, 'weather_duration_days')
        assert world_manager.weather_duration_days > 0

    def test_weather_duration(self, world_manager):
        """Test that weather changes only when the day changes and duration expires."""
        # Get the initial weather
        initial_weather = world_manager.get_current_weather()
        initial_day = world_manager.last_weather_day
        initial_duration = world_manager.weather_duration_days

        # Simulate same day - weather should not change
        world_manager.ecs_world.total_days = initial_day
        same_day_weather = world_manager.get_current_weather()
        assert same_day_weather == initial_weather

        # Simulate next day but within duration - weather should not change
        world_manager.ecs_world.total_days = initial_day + 1
        if initial_duration > 1:  # Only test if duration is more than 1 day
            next_day_weather = world_manager.get_current_weather()
            assert next_day_weather == initial_weather

        # Simulate day after duration expires - weather should change
        world_manager.ecs_world.total_days = initial_day + initial_duration
        world_manager.last_weather_day = initial_day  # Ensure we're testing the day change
        new_weather = world_manager.get_current_weather()

        # Verify the weather duration was reset and last weather day was updated
        assert world_manager.last_weather_day == initial_day + initial_duration
        assert world_manager.weather_duration_days > 0

    def test_seasonal_weather_weights(self):
        """Test that weather probabilities are adjusted based on the season."""
        # Create a new world manager with known season modifiers
        manager = MockWorldManager()

        # Test that different seasons produce different weather distributions
        # We'll do this by setting the game days to different seasons and counting weather occurrences

        # Set to Spring
        manager.ecs_world.game_days = 15  # Middle of Spring
        spring_weather_counts = {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0}

        # Set a fixed seed for reproducibility
        random.seed(42)

        # Generate 100 weather samples for Spring
        for i in range(100):
            # Simulate a new day with expired duration
            manager.ecs_world.total_days = i
            manager.last_weather_day = i - 1
            manager.weather_duration_days = 1

            weather = manager.get_current_weather()
            spring_weather_counts[weather] += 1

        # Set to Summer
        manager.ecs_world.game_days = 45  # Middle of Summer
        summer_weather_counts = {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0}

        # Reset seed
        random.seed(42)

        # Generate 100 weather samples for Summer
        for i in range(100):
            # Simulate a new day with expired duration
            manager.ecs_world.total_days = i
            manager.last_weather_day = i - 1
            manager.weather_duration_days = 1

            weather = manager.get_current_weather()
            summer_weather_counts[weather] += 1

        # The distributions should be different for different seasons
        assert spring_weather_counts != summer_weather_counts

        # Spring should have more Rain than Summer (based on our season modifiers)
        assert spring_weather_counts["Rain"] > summer_weather_counts["Rain"]

        # Summer should have more Clear weather than Spring
        assert summer_weather_counts["Clear"] > spring_weather_counts["Clear"]

    def test_multi_year_weather_simulation(self):
        """Test weather changes over multiple years, ensuring seasonal patterns are maintained."""
        # Create a new world manager for multi-year simulation
        manager = MockWorldManager()

        # Set a fixed seed for reproducibility
        random.seed(42)

        # Define constants for our simulation
        DAYS_PER_SEASON = 30
        DAYS_PER_YEAR = DAYS_PER_SEASON * 4
        YEARS_TO_SIMULATE = 5
        TOTAL_DAYS = DAYS_PER_YEAR * YEARS_TO_SIMULATE

        # Track weather by season across years
        seasonal_weather = {
            "Spring": {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0},
            "Summer": {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0},
            "Fall": {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0},
            "Winter": {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0}
        }

        # Track weather changes
        weather_changes = 0
        last_weather = None
        weather_durations = []
        current_duration = 0

        # Simulate days passing
        for day in range(TOTAL_DAYS):
            # Update the game day and total days
            manager.ecs_world.game_days = day % DAYS_PER_YEAR
            manager.ecs_world.total_days = day

            # Get current season
            current_season, _, _, _ = manager.get_current_season_and_year()

            # Get weather for this day
            weather = manager.get_current_weather()

            # Track weather by season
            seasonal_weather[current_season][weather] += 1

            # Track weather changes
            if last_weather is not None and weather != last_weather:
                weather_changes += 1
                weather_durations.append(current_duration)
                current_duration = 1
            else:
                current_duration += 1

            last_weather = weather

        # Add the final duration
        if current_duration > 0:
            weather_durations.append(current_duration)

        # Calculate average weather duration
        avg_duration = sum(weather_durations) / len(weather_durations) if weather_durations else 0

        # Verify we have weather data for all seasons
        for season, counts in seasonal_weather.items():
            total_days = sum(counts.values())
            assert total_days > 0, f"No weather data for {season}"

        # Verify seasonal patterns
        # Spring should have more rain than summer
        assert seasonal_weather["Spring"]["Rain"] > seasonal_weather["Summer"]["Rain"]

        # Summer should have more clear days than winter
        assert seasonal_weather["Summer"]["Clear"] > seasonal_weather["Winter"]["Clear"]

        # Winter should have more cloudy days than summer
        assert seasonal_weather["Winter"]["Cloudy"] > seasonal_weather["Summer"]["Cloudy"]

        # Verify weather changes appropriately
        # Weather should change multiple times but not every day
        assert weather_changes > 0, "Weather never changed"
        assert weather_changes < TOTAL_DAYS, "Weather changed every day"

        # Average duration should be between 1-3 days (our configured range)
        assert 1 <= avg_duration <= 3, f"Average weather duration {avg_duration} outside expected range"

        # Print statistics for debugging
        print(f"\nWeather simulation over {YEARS_TO_SIMULATE} years ({TOTAL_DAYS} days):")
        print(f"Total weather changes: {weather_changes}")
        print(f"Average weather duration: {avg_duration:.2f} days")
        print("\nSeasonal weather distribution:")

        # Force output to be displayed during pytest run
        import sys
        sys.stdout.flush()

        # Create a more detailed analysis of weather patterns
        yearly_analysis = {}
        for year in range(YEARS_TO_SIMULATE):
            year_start = year * DAYS_PER_YEAR
            year_end = (year + 1) * DAYS_PER_YEAR
            yearly_analysis[year] = {
                "Spring": {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0},
                "Summer": {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0},
                "Fall": {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0},
                "Winter": {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0}
            }

        # Rerun the simulation to collect yearly data
        last_weather = None
        for day in range(TOTAL_DAYS):
            # Update the game day and total days
            manager.ecs_world.game_days = day % DAYS_PER_YEAR
            manager.ecs_world.total_days = day

            # Get current season and year
            current_season, _, _, _ = manager.get_current_season_and_year()
            current_year = day // DAYS_PER_YEAR

            # Get weather for this day
            weather = manager.get_current_weather()

            # Track in yearly analysis
            yearly_analysis[current_year][current_season][weather] += 1

            # Track weather transitions for analysis
            if last_weather is not None and weather != last_weather:
                # Could log transitions here if needed
                pass

            last_weather = weather

        # Print overall seasonal distribution
        for season, counts in seasonal_weather.items():
            total = sum(counts.values())
            print(f"\n{season} ({total} days):")
            for weather, count in sorted(counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total) * 100 if total > 0 else 0
                print(f"  {weather}: {count} days ({percentage:.1f}%)")

        # Print year-by-year analysis
        print("\nYear-by-year weather analysis:")
        for year, seasons in yearly_analysis.items():
            print(f"\nYear {year+1}:")
            for season, counts in seasons.items():
                total = sum(counts.values())
                if total > 0:  # Only print if we have data
                    print(f"  {season}:")
                    for weather, count in sorted(counts.items(), key=lambda x: x[1], reverse=True):
                        percentage = (count / total) * 100 if total > 0 else 0
                        print(f"    {weather}: {count} days ({percentage:.1f}%)")

        # Don't return a value to avoid pytest warning

    def test_weather_effect_on_resources(self):
        """Test that weather affects resource production."""
        # Create a world and system
        world = MockECSWorld()

        # Create weather data
        weather_data = {
            "Clear": {
                "resource_modifiers": {
                    "grain": 1.0,
                    "wood": 1.0
                }
            },
            "Rain": {
                "resource_modifiers": {
                    "grain": 1.5,
                    "wood": 0.8
                }
            }
        }

        # Create seasonal data
        seasonal_data = {
            "Spring": {
                "modifier": 1.0
            }
        }

        # Create a resource production system
        system = MockResourceProductionSystem(weather_data, seasonal_data)
        world.add_system(system)

        # Create a settlement entity
        entity = world.create_entity()
        settlement = MockSettlementComponent("Test Settlement", "Forest", "Faction",
                                         {"grain": 10, "wood": 10}, {})
        inventory = MockInventoryComponent({})
        entity.add_component(settlement)
        entity.add_component(inventory)
        system.entities.append(entity)

        # Update with Clear weather
        system.update(1.0, current_season="Spring", current_weather="Clear")

        # Check resource production with Clear weather
        assert inventory.items["grain"] == 10  # 10 * 1.0 * 1.0
        assert inventory.items["wood"] == 10   # 10 * 1.0 * 1.0

        # Reset inventory
        inventory.items = {}

        # Update with Rain weather
        system.update(1.0, current_season="Spring", current_weather="Rain")

        # Check resource production with Rain weather
        assert inventory.items["grain"] == 15  # 10 * 1.0 * 1.5
        assert inventory.items["wood"] == 8    # 10 * 1.0 * 0.8

    def test_weather_randomization(self, world_manager):
        """Test that weather is properly randomized."""
        # Set a fixed seed for reproducibility
        random.seed(42)

        # Get weather multiple times and count occurrences
        weather_counts = {"Clear": 0, "Cloudy": 0, "Rain": 0, "Storm": 0}

        for i in range(100):
            # Simulate a new day with expired duration
            world_manager.ecs_world.total_days = i
            world_manager.last_weather_day = i - 1
            world_manager.weather_duration_days = 1

            # Get the weather for this day
            weather = world_manager.get_current_weather()
            weather_counts[weather] += 1

        # All weather types should occur at least once
        for weather_type, count in weather_counts.items():
            assert count > 0, f"Weather type {weather_type} never occurred"

        # Clear should be more common than Storm due to weight differences
        assert weather_counts["Clear"] > weather_counts["Storm"]
