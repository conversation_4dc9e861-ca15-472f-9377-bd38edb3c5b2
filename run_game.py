#!/usr/bin/env python3

import sys
import os

# Add the project root and src directory to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), 'living_world_rpg'))
src_dir = os.path.join(project_root, 'src')

# Add both to the path if not already there
if project_root not in sys.path:
    sys.path.insert(0, project_root)
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Now import and run the game
from main import GameApp

if __name__ == "__main__":
    app = GameApp()
    app.run()
